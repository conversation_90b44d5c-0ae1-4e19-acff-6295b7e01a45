import React, { useState } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar,
  Chip,
  IconButton,
  useMediaQuery,
  useTheme,
  Collapse,
} from '@mui/material';
import {
  Dashboard,
  Group,
  AccountBalanceWallet,
  Settings,
  Help,
  Logout,
  ChevronLeft,
  ChevronRight,
  TrendingUp,
  Diamond,
  Star,
  ExpandLess,
  ExpandMore,
  Link as LinkIcon,
  Analytics,
} from '@mui/icons-material';
import { modernColors } from '../../theme/modernTheme';
import { useMining } from '../../context/MiningContext';

interface ModernSidebarProps {
  open: boolean;
  onClose: () => void;
  currentPage: string;
  onPageChange: (page: string) => void;
}

const ModernSidebar: React.FC<ModernSidebarProps> = ({
  open,
  onClose,
  currentPage,
  onPageChange,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [collapsed, setCollapsed] = useState(false);
  const [referralsOpen, setReferralsOpen] = useState(false);
  
  const {
    address,
    userRecord,
    directReferralCount,
    disconnectWallet,
  } = useMining();

  const drawerWidth = collapsed ? 80 : 280;

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Dashboard />,
      path: 'dashboard',
    },
    {
      id: 'referrals',
      label: 'Referrals',
      icon: <Group />,
      path: 'referrals',
      badge: directReferralCount > 0 ? directReferralCount.toString() : undefined,
      submenu: [
        {
          id: 'my-referrals',
          label: 'My Referrals',
          icon: <Group />,
          path: 'referrals',
        },
        {
          id: 'referral-links',
          label: 'Referral Links',
          icon: <LinkIcon />,
          path: 'referral-links',
        },
        {
          id: 'referral-stats',
          label: 'Statistics',
          icon: <Analytics />,
          path: 'referral-stats',
        },
      ],
    },
    {
      id: 'wallet',
      label: 'Wallet',
      icon: <AccountBalanceWallet />,
      path: 'wallet',
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <TrendingUp />,
      path: 'analytics',
    },
  ];

  const bottomMenuItems = [
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings />,
      path: 'settings',
    },
    {
      id: 'help',
      label: 'Help & Support',
      icon: <Help />,
      path: 'help',
    },
  ];

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getUserRank = () => {
    if (!userRecord) return 'Newcomer';
    const totalMinted = Number(userRecord.totalMinted) / 1e18;
    if (totalMinted >= 10000) return 'Diamond';
    if (totalMinted >= 5000) return 'Gold';
    if (totalMinted >= 1000) return 'Silver';
    if (totalMinted >= 100) return 'Bronze';
    return 'Newcomer';
  };

  const getRankColor = () => {
    const rank = getUserRank();
    switch (rank) {
      case 'Diamond': return modernColors.accent.cyan;
      case 'Gold': return modernColors.accent.gold;
      case 'Silver': return '#c0c0c0';
      case 'Bronze': return '#cd7f32';
      default: return modernColors.primary.main;
    }
  };

  const handleItemClick = (item: any) => {
    if (item.submenu) {
      if (item.id === 'referrals') {
        setReferralsOpen(!referralsOpen);
      }
    } else {
      onPageChange(item.path);
      if (isMobile) {
        onClose();
      }
    }
  };

  const drawerContent = (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: modernColors.gradients.card,
        borderRight: `1px solid ${modernColors.dark.border.primary}`,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: collapsed ? 1 : 3,
          borderBottom: `1px solid ${modernColors.dark.border.primary}`,
          background: modernColors.gradients.glow,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {!collapsed && (
            <Box>
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 700,
                  background: modernColors.gradients.primary,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Mining Platform
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Crypto Mining Dashboard
              </Typography>
            </Box>
          )}
          
          {!isMobile && (
            <IconButton
              onClick={() => setCollapsed(!collapsed)}
              sx={{
                color: modernColors.primary.main,
                '&:hover': {
                  background: `${modernColors.primary.main}20`,
                },
              }}
            >
              {collapsed ? <ChevronRight /> : <ChevronLeft />}
            </IconButton>
          )}
        </Box>
      </Box>

      {/* User Profile */}
      <Box
        sx={{
          p: collapsed ? 1 : 2,
          borderBottom: `1px solid ${modernColors.dark.border.primary}`,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: collapsed ? 'column' : 'row',
            gap: collapsed ? 1 : 2,
          }}
        >
          <Avatar
            sx={{
              width: collapsed ? 40 : 48,
              height: collapsed ? 40 : 48,
              background: modernColors.gradients.primary,
              border: `2px solid ${getRankColor()}`,
            }}
          >
            <Diamond />
          </Avatar>
          
          {!collapsed && (
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography variant="body1" sx={{ fontWeight: 600 }} noWrap>
                {address ? formatAddress(address) : 'Not Connected'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                <Chip
                  label={getUserRank()}
                  size="small"
                  sx={{
                    background: `${getRankColor()}20`,
                    color: getRankColor(),
                    border: `1px solid ${getRankColor()}40`,
                    fontSize: '0.75rem',
                  }}
                />
                {directReferralCount > 0 && (
                  <Chip
                    label={`${directReferralCount} refs`}
                    size="small"
                    sx={{
                      background: `${modernColors.secondary.main}20`,
                      color: modernColors.secondary.main,
                      border: `1px solid ${modernColors.secondary.main}40`,
                      fontSize: '0.75rem',
                    }}
                  />
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ p: 1 }}>
          {menuItems.map((item) => (
            <Box key={item.id}>
              <ListItem disablePadding sx={{ mb: 0.5 }}>
                <ListItemButton
                  onClick={() => handleItemClick(item)}
                  selected={currentPage === item.path}
                  sx={{
                    borderRadius: 2,
                    minHeight: 48,
                    px: collapsed ? 1 : 2,
                    '&.Mui-selected': {
                      background: modernColors.gradients.primary,
                      '&:hover': {
                        background: modernColors.gradients.primary,
                      },
                    },
                    '&:hover': {
                      background: `${modernColors.primary.main}20`,
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: collapsed ? 'auto' : 40,
                      color: currentPage === item.path ? 'white' : modernColors.primary.main,
                      justifyContent: 'center',
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  
                  {!collapsed && (
                    <>
                      <ListItemText
                        primary={item.label}
                        sx={{
                          '& .MuiListItemText-primary': {
                            fontWeight: currentPage === item.path ? 600 : 400,
                            color: currentPage === item.path ? 'white' : 'text.primary',
                          },
                        }}
                      />
                      
                      {item.badge && (
                        <Chip
                          label={item.badge}
                          size="small"
                          sx={{
                            background: modernColors.secondary.main,
                            color: 'white',
                            fontSize: '0.75rem',
                            height: 20,
                          }}
                        />
                      )}
                      
                      {item.submenu && (
                        <Box sx={{ color: currentPage === item.path ? 'white' : 'text.secondary' }}>
                          {item.id === 'referrals' && referralsOpen ? <ExpandLess /> : <ExpandMore />}
                        </Box>
                      )}
                    </>
                  )}
                </ListItemButton>
              </ListItem>
              
              {/* Submenu */}
              {item.submenu && !collapsed && (
                <Collapse in={referralsOpen} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.submenu.map((subItem) => (
                      <ListItem key={subItem.id} disablePadding sx={{ mb: 0.5 }}>
                        <ListItemButton
                          onClick={() => {
                            onPageChange(subItem.path);
                            if (isMobile) onClose();
                          }}
                          selected={currentPage === subItem.path}
                          sx={{
                            borderRadius: 2,
                            ml: 2,
                            minHeight: 40,
                            '&.Mui-selected': {
                              background: `${modernColors.primary.main}30`,
                            },
                            '&:hover': {
                              background: `${modernColors.primary.main}20`,
                            },
                          }}
                        >
                          <ListItemIcon
                            sx={{
                              minWidth: 32,
                              color: currentPage === subItem.path ? modernColors.primary.main : 'text.secondary',
                            }}
                          >
                            {subItem.icon}
                          </ListItemIcon>
                          <ListItemText
                            primary={subItem.label}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontSize: '0.875rem',
                                fontWeight: currentPage === subItem.path ? 600 : 400,
                              },
                            }}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </Box>
          ))}
        </List>
      </Box>

      {/* Bottom Menu */}
      <Box sx={{ borderTop: `1px solid ${modernColors.dark.border.primary}` }}>
        <List sx={{ p: 1 }}>
          {bottomMenuItems.map((item) => (
            <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => handleItemClick(item)}
                sx={{
                  borderRadius: 2,
                  minHeight: 48,
                  px: collapsed ? 1 : 2,
                  '&:hover': {
                    background: `${modernColors.primary.main}20`,
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: collapsed ? 'auto' : 40,
                    color: 'text.secondary',
                    justifyContent: 'center',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                {!collapsed && (
                  <ListItemText
                    primary={item.label}
                    sx={{
                      '& .MuiListItemText-primary': {
                        fontSize: '0.875rem',
                      },
                    }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
          
          {/* Logout */}
          <ListItem disablePadding>
            <ListItemButton
              onClick={disconnectWallet}
              sx={{
                borderRadius: 2,
                minHeight: 48,
                px: collapsed ? 1 : 2,
                '&:hover': {
                  background: `${modernColors.error}20`,
                },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: collapsed ? 'auto' : 40,
                  color: modernColors.error,
                  justifyContent: 'center',
                }}
              >
                <Logout />
              </ListItemIcon>
              {!collapsed && (
                <ListItemText
                  primary="Disconnect"
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontSize: '0.875rem',
                      color: modernColors.error,
                    },
                  }}
                />
              )}
            </ListItemButton>
          </ListItem>
        </List>
      </Box>
    </Box>
  );

  return (
    <Drawer
      variant={isMobile ? 'temporary' : 'permanent'}
      open={open}
      onClose={onClose}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default ModernSidebar;
