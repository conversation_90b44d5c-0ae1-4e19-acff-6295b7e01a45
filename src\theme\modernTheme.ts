import { createTheme, ThemeOptions } from '@mui/material/styles';

// World-class color palette inspired by top fintech and crypto platforms
export const modernColors = {
  // Primary brand colors - Deep blue to purple gradient
  primary: {
    50: '#f0f4ff',
    100: '#e0e7ff',
    200: '#c7d2fe',
    300: '#a5b4fc',
    400: '#818cf8',
    500: '#6366f1', // Main brand color
    600: '#4f46e5',
    700: '#4338ca',
    800: '#3730a3',
    900: '#312e81',
    main: '#6366f1',
    dark: '#4338ca',
    light: '#818cf8',
  },
  
  // Secondary colors - Emerald green for success/mining
  secondary: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // Main secondary
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
    main: '#10b981',
    dark: '#047857',
    light: '#34d399',
  },
  
  // Accent colors
  accent: {
    gold: '#f59e0b',
    orange: '#f97316',
    pink: '#ec4899',
    purple: '#8b5cf6',
    cyan: '#06b6d4',
  },
  
  // Status colors
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
  
  // Dark theme colors
  dark: {
    background: {
      default: '#0a0a0b',
      paper: '#111111',
      elevated: '#1a1a1b',
    },
    surface: {
      primary: '#1a1a1b',
      secondary: '#242426',
      tertiary: '#2d2d30',
    },
    text: {
      primary: '#ffffff',
      secondary: '#a1a1aa',
      tertiary: '#71717a',
    },
    border: {
      primary: '#27272a',
      secondary: '#3f3f46',
    }
  },
  
  // Gradients for modern look
  gradients: {
    primary: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)',
    secondary: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
    mining: 'linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #ef4444 100%)',
    dark: 'linear-gradient(135deg, #0a0a0b 0%, #1a1a1b 50%, #242426 100%)',
    card: 'linear-gradient(135deg, #111111 0%, #1a1a1b 100%)',
    glow: 'linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%)',
  }
};

// Animation keyframes
export const animations = {
  glow: {
    '@keyframes glow': {
      '0%': {
        boxShadow: '0 0 5px rgba(99, 102, 241, 0.5)',
      },
      '50%': {
        boxShadow: '0 0 20px rgba(99, 102, 241, 0.8), 0 0 30px rgba(139, 92, 246, 0.6)',
      },
      '100%': {
        boxShadow: '0 0 5px rgba(99, 102, 241, 0.5)',
      },
    },
  },
  
  pulse: {
    '@keyframes pulse': {
      '0%': {
        transform: 'scale(1)',
        opacity: 1,
      },
      '50%': {
        transform: 'scale(1.05)',
        opacity: 0.8,
      },
      '100%': {
        transform: 'scale(1)',
        opacity: 1,
      },
    },
  },
  
  mining: {
    '@keyframes mining': {
      '0%': {
        transform: 'translateY(0px) rotate(0deg)',
        opacity: 0.7,
      },
      '25%': {
        transform: 'translateY(-10px) rotate(90deg)',
        opacity: 1,
      },
      '50%': {
        transform: 'translateY(-5px) rotate(180deg)',
        opacity: 0.8,
      },
      '75%': {
        transform: 'translateY(-15px) rotate(270deg)',
        opacity: 1,
      },
      '100%': {
        transform: 'translateY(0px) rotate(360deg)',
        opacity: 0.7,
      },
    },
  },
  
  float: {
    '@keyframes float': {
      '0%': {
        transform: 'translateY(0px)',
      },
      '50%': {
        transform: 'translateY(-20px)',
      },
      '100%': {
        transform: 'translateY(0px)',
      },
    },
  },
  
  shimmer: {
    '@keyframes shimmer': {
      '0%': {
        backgroundPosition: '-200% 0',
      },
      '100%': {
        backgroundPosition: '200% 0',
      },
    },
  },
};

// Create the modern theme
export const createModernTheme = (): ThemeOptions => ({
  palette: {
    mode: 'dark',
    primary: modernColors.primary,
    secondary: modernColors.secondary,
    background: {
      default: modernColors.dark.background.default,
      paper: modernColors.dark.background.paper,
    },
    text: {
      primary: modernColors.dark.text.primary,
      secondary: modernColors.dark.text.secondary,
    },
    success: {
      main: modernColors.success,
    },
    warning: {
      main: modernColors.warning,
    },
    error: {
      main: modernColors.error,
    },
    info: {
      main: modernColors.info,
    },
  },
  
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '3.5rem',
      lineHeight: 1.2,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontWeight: 700,
      fontSize: '2.5rem',
      lineHeight: 1.3,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontWeight: 600,
      fontSize: '2rem',
      lineHeight: 1.4,
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.4,
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
      lineHeight: 1.5,
    },
    h6: {
      fontWeight: 600,
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
  },
  
  shape: {
    borderRadius: 12,
  },
  
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          ...animations.glow,
          ...animations.pulse,
          ...animations.mining,
          ...animations.float,
          ...animations.shimmer,
          background: modernColors.gradients.dark,
          minHeight: '100vh',
        },
      },
    },
    
    MuiCard: {
      styleOverrides: {
        root: {
          background: modernColors.gradients.card,
          border: `1px solid ${modernColors.dark.border.primary}`,
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
            border: `1px solid ${modernColors.primary.main}`,
          },
        },
      },
    },
    
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          borderRadius: 8,
          padding: '12px 24px',
          transition: 'all 0.2s ease-in-out',
        },
        contained: {
          background: modernColors.gradients.primary,
          boxShadow: '0 4px 14px rgba(99, 102, 241, 0.3)',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(99, 102, 241, 0.4)',
          },
        },
      },
    },
    
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            background: modernColors.dark.surface.secondary,
            borderRadius: 8,
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: modernColors.primary.main,
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: modernColors.primary.main,
              boxShadow: `0 0 0 2px ${modernColors.primary.main}20`,
            },
          },
        },
      },
    },
  },
});

export default createModernTheme;
