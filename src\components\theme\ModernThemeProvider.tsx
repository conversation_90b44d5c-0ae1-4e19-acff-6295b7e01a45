import React from 'react';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import { createModernTheme } from '../../theme/modernTheme';

interface ModernThemeProviderProps {
  children: React.ReactNode;
}

const ModernThemeProvider: React.FC<ModernThemeProviderProps> = ({ children }) => {
  const theme = createTheme(createModernTheme());

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

export default ModernThemeProvider;
