import React, { useState } from 'react';
import { Box, useMediaQuery, useTheme } from '@mui/material';
import ModernSidebar from './ModernSidebar';
import ModernHeader from './ModernHeader';
import { modernColors } from '../../theme/modernTheme';

interface DashboardLayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onPageChange: (page: string) => void;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  currentPage,
  onPageChange,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        background: modernColors.gradients.dark,
      }}
    >
      {/* Sidebar */}
      <ModernSidebar
        open={sidebarOpen}
        onClose={handleSidebarClose}
        currentPage={currentPage}
        onPageChange={onPageChange}
      />

      {/* Main Content Area */}
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          marginLeft: isMobile ? 0 : sidebarOpen ? '280px' : '80px',
          transition: theme.transitions.create('margin', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
        }}
      >
        {/* Header */}
        <ModernHeader
          onMenuClick={handleSidebarToggle}
          sidebarOpen={sidebarOpen}
        />

        {/* Page Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            pt: 8, // Account for fixed header
            minHeight: 'calc(100vh - 64px)',
            background: 'transparent',
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default DashboardLayout;
