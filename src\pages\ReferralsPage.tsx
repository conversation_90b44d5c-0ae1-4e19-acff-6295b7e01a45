import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  Tooltip,
  Avatar,
  LinearProgress,
  Fade,
  Zoom,
} from '@mui/material';
import {
  ContentCopy,
  Share,
  Person,
  TrendingUp,
  Link as LinkIcon,
  QrCode,
  Add,
  Group,
  Diamond,
  Star,
  EmojiEvents,
} from '@mui/icons-material';
import { modernColors } from '../theme/modernTheme';
import { useMining } from '../context/MiningContext';

const ReferralsPage: React.FC = () => {
  const {
    address,
    directReferrals,
    directReferralCount,
    userRecord,
  } = useMining();

  const [referralLink, setReferralLink] = useState('');
  const [customSlug, setCustomSlug] = useState('');
  const [copied, setCopied] = useState(false);
  const [shareSuccess, setShareSuccess] = useState(false);

  useEffect(() => {
    if (address) {
      const baseUrl = `${window.location.origin}${window.location.pathname}`;
      const link = customSlug 
        ? `${baseUrl}?ref=${address}&slug=${encodeURIComponent(customSlug)}`
        : `${baseUrl}?ref=${address}`;
      setReferralLink(link);
    }
  }, [address, customSlug]);

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 8)}...${addr.slice(-6)}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 3000);
  };

  const shareReferralLink = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Join Crypto Mining Platform',
        text: 'Join me on this amazing crypto mining platform and start earning tokens!',
        url: referralLink,
      }).then(() => {
        setShareSuccess(true);
        setTimeout(() => setShareSuccess(false), 3000);
      });
    } else {
      copyToClipboard(referralLink);
    }
  };

  const getReferralStats = () => {
    const totalEarnings = userRecord ? Number(userRecord.totalMinted) / 1e18 : 0;
    const avgPerReferral = directReferralCount > 0 ? totalEarnings / directReferralCount : 0;
    
    return {
      totalReferrals: directReferralCount,
      totalEarnings,
      avgPerReferral,
      rank: getRank(),
    };
  };

  const getRank = () => {
    if (directReferralCount >= 100) return { name: 'Diamond Ambassador', color: modernColors.accent.cyan, icon: <Diamond /> };
    if (directReferralCount >= 50) return { name: 'Gold Leader', color: modernColors.accent.gold, icon: <EmojiEvents /> };
    if (directReferralCount >= 25) return { name: 'Silver Recruiter', color: '#c0c0c0', icon: <Star /> };
    if (directReferralCount >= 10) return { name: 'Bronze Networker', color: '#cd7f32', icon: <Group /> };
    if (directReferralCount >= 1) return { name: 'Active Referrer', color: modernColors.primary.main, icon: <Person /> };
    return { name: 'Newcomer', color: modernColors.dark.text.secondary, icon: <Add /> };
  };

  const stats = getReferralStats();

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Fade in timeout={600}>
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 700,
              background: modernColors.gradients.primary,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1,
            }}
          >
            Referral Dashboard
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Grow your network and earn more rewards
          </Typography>
        </Box>
      </Fade>

      <Grid container spacing={3}>
        {/* Stats Cards */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={6} sm={3}>
              <Zoom in timeout={800}>
                <Card
                  sx={{
                    background: `linear-gradient(135deg, ${modernColors.primary.main}20, ${modernColors.primary.dark}20)`,
                    border: `1px solid ${modernColors.primary.main}40`,
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 2 }}>
                    <Group sx={{ fontSize: 32, color: modernColors.primary.main, mb: 1 }} />
                    <Typography variant="h4" sx={{ fontWeight: 700, color: modernColors.primary.main }}>
                      {stats.totalReferrals}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Referrals
                    </Typography>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>

            <Grid item xs={6} sm={3}>
              <Zoom in timeout={1000}>
                <Card
                  sx={{
                    background: `linear-gradient(135deg, ${modernColors.secondary.main}20, ${modernColors.secondary.dark}20)`,
                    border: `1px solid ${modernColors.secondary.main}40`,
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 2 }}>
                    <TrendingUp sx={{ fontSize: 32, color: modernColors.secondary.main, mb: 1 }} />
                    <Typography variant="h4" sx={{ fontWeight: 700, color: modernColors.secondary.main }}>
                      {stats.totalEarnings.toFixed(2)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Earned
                    </Typography>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>

            <Grid item xs={6} sm={3}>
              <Zoom in timeout={1200}>
                <Card
                  sx={{
                    background: `linear-gradient(135deg, ${modernColors.accent.gold}20, ${modernColors.accent.orange}20)`,
                    border: `1px solid ${modernColors.accent.gold}40`,
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 2 }}>
                    <Diamond sx={{ fontSize: 32, color: modernColors.accent.gold, mb: 1 }} />
                    <Typography variant="h4" sx={{ fontWeight: 700, color: modernColors.accent.gold }}>
                      {stats.avgPerReferral.toFixed(2)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg per Referral
                    </Typography>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>

            <Grid item xs={6} sm={3}>
              <Zoom in timeout={1400}>
                <Card
                  sx={{
                    background: `linear-gradient(135deg, ${stats.rank.color}20, ${stats.rank.color}10)`,
                    border: `1px solid ${stats.rank.color}40`,
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 2 }}>
                    <Box sx={{ color: stats.rank.color, mb: 1 }}>
                      {React.cloneElement(stats.rank.icon, { sx: { fontSize: 32 } })}
                    </Box>
                    <Typography variant="body1" sx={{ fontWeight: 700, color: stats.rank.color }}>
                      {stats.rank.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Current Rank
                    </Typography>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          </Grid>

          {/* Referral Link Generator */}
          <Fade in timeout={1600}>
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                  <LinkIcon sx={{ mr: 1, color: modernColors.primary.main }} />
                  Your Referral Link
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <TextField
                    fullWidth
                    label="Custom Slug (Optional)"
                    value={customSlug}
                    onChange={(e) => setCustomSlug(e.target.value)}
                    placeholder="my-awesome-link"
                    helperText="Create a custom, memorable referral link"
                    sx={{ mb: 2 }}
                  />

                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      p: 2,
                      background: modernColors.dark.surface.secondary,
                      borderRadius: 2,
                      border: `1px solid ${modernColors.dark.border.primary}`,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        flex: 1,
                        fontFamily: 'monospace',
                        wordBreak: 'break-all',
                        color: modernColors.primary.main,
                      }}
                    >
                      {referralLink}
                    </Typography>
                    
                    <Tooltip title={copied ? "Copied!" : "Copy Link"}>
                      <IconButton
                        onClick={() => copyToClipboard(referralLink)}
                        sx={{
                          color: copied ? modernColors.success : modernColors.primary.main,
                        }}
                      >
                        <ContentCopy />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Share Link">
                      <IconButton
                        onClick={shareReferralLink}
                        sx={{ color: modernColors.secondary.main }}
                      >
                        <Share />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>

                {(copied || shareSuccess) && (
                  <Alert severity="success" sx={{ mb: 2 }}>
                    {copied ? "🎉 Referral link copied to clipboard!" : "🚀 Link shared successfully!"}
                  </Alert>
                )}

                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    startIcon={<ContentCopy />}
                    onClick={() => copyToClipboard(referralLink)}
                    sx={{
                      background: modernColors.gradients.primary,
                      '&:hover': {
                        background: modernColors.gradients.primary,
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    Copy Link
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<Share />}
                    onClick={shareReferralLink}
                    sx={{
                      borderColor: modernColors.secondary.main,
                      color: modernColors.secondary.main,
                      '&:hover': {
                        borderColor: modernColors.secondary.main,
                        background: `${modernColors.secondary.main}20`,
                      },
                    }}
                  >
                    Share
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<QrCode />}
                    sx={{
                      borderColor: modernColors.accent.purple,
                      color: modernColors.accent.purple,
                      '&:hover': {
                        borderColor: modernColors.accent.purple,
                        background: `${modernColors.accent.purple}20`,
                      },
                    }}
                  >
                    QR Code
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Fade>
        </Grid>

        {/* Rank Progress */}
        <Grid item xs={12} md={4}>
          <Fade in timeout={1800}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                  <EmojiEvents sx={{ mr: 1, color: modernColors.accent.gold }} />
                  Rank Progress
                </Typography>

                <Box sx={{ textAlign: 'center', mb: 3 }}>
                  <Box sx={{ color: stats.rank.color, mb: 2 }}>
                    {React.cloneElement(stats.rank.icon, { sx: { fontSize: 48 } })}
                  </Box>
                  <Typography variant="h6" sx={{ color: stats.rank.color, fontWeight: 700 }}>
                    {stats.rank.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {directReferralCount} referrals
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Next Rank</Typography>
                    <Typography variant="body2">
                      {directReferralCount < 10 ? '10' : 
                       directReferralCount < 25 ? '25' : 
                       directReferralCount < 50 ? '50' : 
                       directReferralCount < 100 ? '100' : 'Max'}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={
                      directReferralCount >= 100 ? 100 :
                      directReferralCount >= 50 ? ((directReferralCount - 50) / 50) * 100 :
                      directReferralCount >= 25 ? ((directReferralCount - 25) / 25) * 100 :
                      directReferralCount >= 10 ? ((directReferralCount - 10) / 15) * 100 :
                      (directReferralCount / 10) * 100
                    }
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: modernColors.dark.surface.secondary,
                      '& .MuiLinearProgress-bar': {
                        background: modernColors.gradients.primary,
                        borderRadius: 4,
                      },
                    }}
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                  {directReferralCount >= 100 ? 'Maximum rank achieved!' :
                   `${(directReferralCount < 10 ? 10 - directReferralCount :
                       directReferralCount < 25 ? 25 - directReferralCount :
                       directReferralCount < 50 ? 50 - directReferralCount :
                       100 - directReferralCount)} more referrals to next rank`}
                </Typography>
              </CardContent>
            </Card>
          </Fade>
        </Grid>

        {/* Referral List */}
        <Grid item xs={12}>
          <Fade in timeout={2000}>
            <Card>
              <CardContent>
                <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                  <Group sx={{ mr: 1, color: modernColors.primary.main }} />
                  Your Referrals ({directReferralCount})
                </Typography>

                {directReferrals.length > 0 ? (
                  <List>
                    {directReferrals.map((referral, index) => (
                      <React.Fragment key={referral}>
                        <ListItem
                          sx={{
                            borderRadius: 2,
                            mb: 1,
                            background: modernColors.dark.surface.secondary,
                            '&:hover': {
                              background: modernColors.dark.surface.tertiary,
                            },
                          }}
                        >
                          <ListItemIcon>
                            <Avatar
                              sx={{
                                width: 40,
                                height: 40,
                                background: modernColors.gradients.primary,
                              }}
                            >
                              {index + 1}
                            </Avatar>
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography
                                  variant="body1"
                                  sx={{ fontFamily: 'monospace', fontWeight: 600 }}
                                >
                                  {formatAddress(referral)}
                                </Typography>
                                <Chip
                                  label="Active"
                                  size="small"
                                  sx={{
                                    background: `${modernColors.success}20`,
                                    color: modernColors.success,
                                    border: `1px solid ${modernColors.success}40`,
                                  }}
                                />
                              </Box>
                            }
                            secondary={`Referral #${index + 1} • Joined recently`}
                          />
                          <IconButton
                            onClick={() => copyToClipboard(referral)}
                            sx={{ color: modernColors.primary.main }}
                          >
                            <ContentCopy />
                          </IconButton>
                        </ListItem>
                        {index < directReferrals.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Group sx={{ fontSize: 64, color: modernColors.dark.text.tertiary, mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                      No referrals yet
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Share your referral link to start building your network!
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Fade>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReferralsPage;
