import React, { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  CircularProgress,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Fade,
  Zoom,
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  Refresh,
  TrendingUp,
  Timer,
  Diamond,
  Bolt,
} from '@mui/icons-material';
import { modernColors } from '../../theme/modernTheme';
import { useMining } from '../../context/MiningContext';
import MiningAnimations from '../animations/MiningAnimations';

interface MiningStatusCardProps {
  onClaim: () => Promise<void>;
  claiming: boolean;
}

const MiningStatusCard: React.FC<MiningStatusCardProps> = ({ onClaim, claiming }) => {
  const {
    userRecord,
    dailyReward,
    canClaim,
    timeUntilNextClaim,
    refreshData,
  } = useMining();

  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeDisplay, setTimeDisplay] = useState('');

  const formatAmount = (amount: bigint) => {
    return (Number(amount) / 1e18).toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    });
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Update time display every second
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeDisplay(formatTime(timeUntilNextClaim));
    }, 1000);

    return () => clearInterval(interval);
  }, [timeUntilNextClaim]);

  // Auto-refresh when mining status becomes zero
  useEffect(() => {
    if (autoRefresh && timeUntilNextClaim === 0 && !canClaim) {
      const autoRefreshTimer = setTimeout(async () => {
        setRefreshing(true);
        await refreshData();
        setRefreshing(false);
      }, 1000);

      return () => clearTimeout(autoRefreshTimer);
    }
  }, [timeUntilNextClaim, canClaim, autoRefresh, refreshData]);

  const handleManualRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    setRefreshing(false);
  };

  const getMiningProgress = () => {
    const totalTime = 24 * 60 * 60; // 24 hours in seconds
    const elapsed = totalTime - timeUntilNextClaim;
    return Math.min((elapsed / totalTime) * 100, 100);
  };

  const getMiningIntensity = () => {
    const progress = getMiningProgress();
    if (progress > 80) return 'high';
    if (progress > 40) return 'medium';
    return 'low';
  };

  const isMining = timeUntilNextClaim > 0 || canClaim;

  return (
    <Fade in timeout={800}>
      <Card
        sx={{
          position: 'relative',
          background: modernColors.gradients.card,
          border: `1px solid ${modernColors.dark.border.primary}`,
          borderRadius: 3,
          overflow: 'hidden',
          minHeight: 400,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            background: canClaim ? modernColors.gradients.secondary : modernColors.gradients.primary,
          },
        }}
      >
        {/* Mining Animations Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.3,
            zIndex: 1,
          }}
        >
          <MiningAnimations
            isActive={isMining}
            intensity={getMiningIntensity()}
            showParticles={true}
          />
        </Box>

        <CardContent sx={{ position: 'relative', zIndex: 2, p: 3 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 700,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Diamond sx={{ color: modernColors.primary.main }} />
              Mining Status
            </Typography>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Toggle Auto-refresh">
                <IconButton
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  sx={{
                    color: autoRefresh ? modernColors.success : modernColors.dark.text.secondary,
                    '&:hover': {
                      background: `${modernColors.primary.main}20`,
                    },
                  }}
                >
                  {autoRefresh ? <PlayArrow /> : <Pause />}
                </IconButton>
              </Tooltip>

              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={handleManualRefresh}
                  disabled={refreshing}
                  sx={{
                    color: modernColors.primary.main,
                    '&:hover': {
                      background: `${modernColors.primary.main}20`,
                    },
                  }}
                >
                  {refreshing ? <CircularProgress size={20} /> : <Refresh />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Mining Progress */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                Mining Progress
              </Typography>
              <Chip
                label={canClaim ? 'Ready to Claim' : isMining ? 'Mining...' : 'Idle'}
                size="small"
                sx={{
                  background: canClaim 
                    ? `${modernColors.success}20` 
                    : isMining 
                      ? `${modernColors.primary.main}20` 
                      : `${modernColors.dark.text.secondary}20`,
                  color: canClaim 
                    ? modernColors.success 
                    : isMining 
                      ? modernColors.primary.main 
                      : modernColors.dark.text.secondary,
                  border: `1px solid ${canClaim 
                    ? modernColors.success 
                    : isMining 
                      ? modernColors.primary.main 
                      : modernColors.dark.text.secondary}40`,
                }}
              />
            </Box>

            <LinearProgress
              variant="determinate"
              value={getMiningProgress()}
              sx={{
                height: 12,
                borderRadius: 6,
                backgroundColor: modernColors.dark.surface.secondary,
                '& .MuiLinearProgress-bar': {
                  background: canClaim 
                    ? modernColors.gradients.secondary 
                    : modernColors.gradients.primary,
                  borderRadius: 6,
                  transition: 'all 0.3s ease',
                },
              }}
            />

            <Typography variant="body2" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
              {getMiningProgress().toFixed(1)}% Complete
            </Typography>
          </Box>

          {/* Reward Information */}
          <Box sx={{ mb: 4 }}>
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: 2,
              }}
            >
              <Card
                sx={{
                  background: `linear-gradient(135deg, ${modernColors.accent.gold}20, ${modernColors.accent.orange}20)`,
                  border: `1px solid ${modernColors.accent.gold}40`,
                  p: 2,
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Bolt sx={{ color: modernColors.accent.gold, mr: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Daily Reward
                  </Typography>
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 700, color: modernColors.accent.gold }}>
                  {formatAmount(dailyReward)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Tokens
                </Typography>
              </Card>

              <Card
                sx={{
                  background: `linear-gradient(135deg, ${modernColors.secondary.main}20, ${modernColors.secondary.dark}20)`,
                  border: `1px solid ${modernColors.secondary.main}40`,
                  p: 2,
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TrendingUp sx={{ color: modernColors.secondary.main, mr: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Total Mined
                  </Typography>
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 700, color: modernColors.secondary.main }}>
                  {userRecord ? formatAmount(userRecord.totalMinted) : '0.00'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Tokens
                </Typography>
              </Card>
            </Box>
          </Box>

          {/* Timer and Claim Button */}
          <Box sx={{ textAlign: 'center' }}>
            {canClaim ? (
              <Zoom in timeout={600}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={onClaim}
                  disabled={claiming}
                  startIcon={claiming ? <CircularProgress size={20} /> : <Diamond />}
                  sx={{
                    py: 2,
                    px: 4,
                    fontSize: '1.1rem',
                    fontWeight: 700,
                    background: modernColors.gradients.secondary,
                    boxShadow: `0 8px 25px ${modernColors.success}40`,
                    '&:hover': {
                      background: modernColors.gradients.secondary,
                      transform: 'translateY(-2px)',
                      boxShadow: `0 12px 35px ${modernColors.success}50`,
                    },
                    '&:disabled': {
                      background: modernColors.dark.surface.secondary,
                      color: modernColors.dark.text.tertiary,
                    },
                  }}
                >
                  {claiming ? 'Claiming...' : 'Claim Rewards'}
                </Button>
              </Zoom>
            ) : timeUntilNextClaim > 0 ? (
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                  <Timer sx={{ color: modernColors.primary.main, mr: 1 }} />
                  <Typography variant="h4" sx={{ fontWeight: 700, fontFamily: 'monospace' }}>
                    {timeDisplay}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Until next claim available
                </Typography>
              </Box>
            ) : (
              <Box>
                <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                  Mining Idle
                </Typography>
                <Button
                  variant="outlined"
                  onClick={handleManualRefresh}
                  disabled={refreshing}
                  startIcon={refreshing ? <CircularProgress size={20} /> : <Refresh />}
                  sx={{
                    borderColor: modernColors.primary.main,
                    color: modernColors.primary.main,
                    '&:hover': {
                      borderColor: modernColors.primary.main,
                      background: `${modernColors.primary.main}20`,
                    },
                  }}
                >
                  {refreshing ? 'Refreshing...' : 'Check Status'}
                </Button>
              </Box>
            )}
          </Box>

          {/* Auto-refresh Indicator */}
          {autoRefresh && (
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Chip
                label="Auto-refresh enabled"
                size="small"
                sx={{
                  background: `${modernColors.success}20`,
                  color: modernColors.success,
                  border: `1px solid ${modernColors.success}40`,
                  fontSize: '0.75rem',
                }}
              />
            </Box>
          )}
        </CardContent>
      </Card>
    </Fade>
  );
};

export default MiningStatusCard;
