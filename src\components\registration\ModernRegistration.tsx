import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  InputAdornment,
  IconButton,
  Tooltip,
  Fade,
  Zoom,
} from '@mui/material';
import {
  PersonAdd,
  Link as LinkIcon,
  ContentCopy,
  CheckCircle,
  Info,
  Rocket,
  Diamond,
} from '@mui/icons-material';
import { modernColors } from '../../theme/modernTheme';

interface ModernRegistrationProps {
  onRegister: (referrerAddress?: string) => Promise<boolean>;
  registering: boolean;
  regReward: bigint;
  dailyReward: bigint;
  formatAmount: (amount: bigint) => string;
}

const ModernRegistration: React.FC<ModernRegistrationProps> = ({
  onRegister,
  registering,
  regReward,
  dailyReward,
  formatAmount,
}) => {
  const [referrerAddress, setReferrerAddress] = useState('');
  const [isValidAddress, setIsValidAddress] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [copied, setCopied] = useState(false);

  // Check if address is valid Ethereum address
  const validateAddress = (address: string) => {
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
    return ethAddressRegex.test(address);
  };

  useEffect(() => {
    if (referrerAddress) {
      setIsValidAddress(validateAddress(referrerAddress));
    } else {
      setIsValidAddress(false);
    }
  }, [referrerAddress]);

  // Get referrer from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const refParam = urlParams.get('ref');
    if (refParam && validateAddress(refParam)) {
      setReferrerAddress(refParam);
    }
  }, []);

  const handleRegister = async () => {
    const success = await onRegister(referrerAddress || undefined);
    if (success) {
      setShowSuccess(true);
    }
  };

  const copyAddress = () => {
    navigator.clipboard.writeText(referrerAddress);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: modernColors.gradients.dark,
        p: 2,
      }}
    >
      <Fade in timeout={800}>
        <Card
          sx={{
            maxWidth: 600,
            width: '100%',
            background: modernColors.gradients.card,
            border: `1px solid ${modernColors.dark.border.primary}`,
            borderRadius: 3,
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: 4,
              background: modernColors.gradients.primary,
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Zoom in timeout={1000}>
                <Box
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: modernColors.gradients.primary,
                    mb: 2,
                    animation: 'glow 2s ease-in-out infinite',
                  }}
                >
                  <Rocket sx={{ fontSize: 40, color: 'white' }} />
                </Box>
              </Zoom>
              
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 700,
                  background: modernColors.gradients.primary,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                }}
              >
                Join Mining Platform
              </Typography>
              
              <Typography variant="h6" color="text.secondary">
                Start your crypto mining journey today
              </Typography>
            </Box>

            {/* Rewards Info */}
            <Box sx={{ mb: 4 }}>
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: 2,
                  mb: 3,
                }}
              >
                <Card
                  sx={{
                    background: `linear-gradient(135deg, ${modernColors.secondary.main}20, ${modernColors.secondary.dark}20)`,
                    border: `1px solid ${modernColors.secondary.main}40`,
                    p: 2,
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Diamond sx={{ color: modernColors.secondary.main, mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      Registration Bonus
                    </Typography>
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 700, color: modernColors.secondary.main }}>
                    {formatAmount(regReward)} Tokens
                  </Typography>
                </Card>

                <Card
                  sx={{
                    background: `linear-gradient(135deg, ${modernColors.accent.gold}20, ${modernColors.accent.orange}20)`,
                    border: `1px solid ${modernColors.accent.gold}40`,
                    p: 2,
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <PersonAdd sx={{ color: modernColors.accent.gold, mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      Daily Rewards
                    </Typography>
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 700, color: modernColors.accent.gold }}>
                    {formatAmount(dailyReward)} Tokens
                  </Typography>
                </Card>
              </Box>
            </Box>

            <Divider sx={{ mb: 3, borderColor: modernColors.dark.border.secondary }} />

            {/* Referrer Input */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <LinkIcon sx={{ mr: 1, color: modernColors.primary.main }} />
                Referrer Address (Optional)
              </Typography>
              
              <TextField
                fullWidth
                label="Enter referrer wallet address"
                value={referrerAddress}
                onChange={(e) => setReferrerAddress(e.target.value)}
                placeholder="0x..."
                disabled={registering}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LinkIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: referrerAddress && (
                    <InputAdornment position="end">
                      {isValidAddress ? (
                        <Tooltip title="Valid address">
                          <CheckCircle sx={{ color: modernColors.success }} />
                        </Tooltip>
                      ) : (
                        <Tooltip title="Copy address">
                          <IconButton onClick={copyAddress} size="small">
                            <ContentCopy />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  ),
                }}
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    '&.Mui-focused': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: isValidAddress ? modernColors.success : modernColors.primary.main,
                      },
                    },
                  },
                }}
              />

              {referrerAddress && (
                <Fade in>
                  <Alert
                    severity={isValidAddress ? "success" : "warning"}
                    sx={{ mb: 2 }}
                    icon={isValidAddress ? <CheckCircle /> : <Info />}
                  >
                    {isValidAddress
                      ? "✅ Valid referrer address - they will earn commissions from your activity"
                      : "⚠️ Please enter a valid Ethereum address (0x...)"
                    }
                  </Alert>
                </Fade>
              )}

              {!referrerAddress && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    💡 Leave empty to register without a referrer. You can still earn from your own referrals!
                  </Typography>
                </Alert>
              )}

              {copied && (
                <Chip
                  label="Address copied!"
                  color="success"
                  size="small"
                  sx={{ mb: 1 }}
                />
              )}
            </Box>

            {/* Register Button */}
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handleRegister}
              disabled={registering || (referrerAddress && !isValidAddress)}
              startIcon={registering ? <CircularProgress size={20} /> : <Rocket />}
              sx={{
                py: 2,
                fontSize: '1.1rem',
                fontWeight: 700,
                background: modernColors.gradients.primary,
                '&:hover': {
                  background: modernColors.gradients.primary,
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(99, 102, 241, 0.4)',
                },
                '&:disabled': {
                  background: modernColors.dark.surface.secondary,
                  color: modernColors.dark.text.tertiary,
                },
              }}
            >
              {registering ? 'Registering...' : 'Start Mining Journey'}
            </Button>

            {showSuccess && (
              <Fade in>
                <Alert severity="success" sx={{ mt: 2 }}>
                  🎉 Registration successful! Welcome to the mining platform!
                </Alert>
              </Fade>
            )}
          </CardContent>
        </Card>
      </Fade>
    </Box>
  );
};

export default ModernRegistration;
