import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  InputAdornment,
  IconButton,
  Tooltip,
  Fade,
  Zoom,
} from '@mui/material';
import {
  PersonAdd,
  Link as LinkIcon,
  ContentCopy,
  CheckCircle,
  Info,
  Rocket,
  Diamond,
} from '@mui/icons-material';
import { modernColors } from '../../theme/modernTheme';

interface ModernRegistrationProps {
  onRegister: (referrerAddress?: string) => Promise<boolean>;
  registering: boolean;
  regReward: bigint;
  dailyReward: bigint;
  formatAmount: (amount: bigint) => string;
}

const ModernRegistration: React.FC<ModernRegistrationProps> = ({
  onRegister,
  registering,
  regReward,
  dailyReward,
  formatAmount,
}) => {
  const [referrerAddress, setReferrerAddress] = useState('');
  const [isValidAddress, setIsValidAddress] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [copied, setCopied] = useState(false);

  // Check if address is valid Ethereum address
  const validateAddress = (address: string) => {
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
    return ethAddressRegex.test(address);
  };

  useEffect(() => {
    if (referrerAddress) {
      setIsValidAddress(validateAddress(referrerAddress));
    } else {
      setIsValidAddress(false);
    }
  }, [referrerAddress]);

  // Get referrer from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const refParam = urlParams.get('ref');
    if (refParam && validateAddress(refParam)) {
      setReferrerAddress(refParam);
    }
  }, []);

  const handleRegister = async () => {
    const success = await onRegister(referrerAddress || undefined);
    if (success) {
      setShowSuccess(true);
    }
  };

  const copyAddress = () => {
    navigator.clipboard.writeText(referrerAddress);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: modernColors.gradients.dark,
        p: 2,
      }}
    >
      <Fade in timeout={800}>
        <Card
          sx={{
            maxWidth: 600,
            width: '100%',
            background: modernColors.gradients.card,
            border: `1px solid ${modernColors.dark.border.primary}`,
            borderRadius: 3,
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: 4,
              background: modernColors.gradients.primary,
            },
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Zoom in timeout={1000}>
                <Box
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: `linear-gradient(135deg, ${modernColors.primary.main}15, ${modernColors.secondary.main}15)`,
                    border: `3px solid ${modernColors.primary.main}30`,
                    mb: 3,
                    animation: 'glow 3s ease-in-out infinite alternate, float 6s ease-in-out infinite',
                    position: 'relative',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      inset: -8,
                      borderRadius: '50%',
                      background: `conic-gradient(from 0deg, ${modernColors.primary.main}40, ${modernColors.secondary.main}40, ${modernColors.accent.purple}40, ${modernColors.primary.main}40)`,
                      animation: 'spin 8s linear infinite',
                      zIndex: -1,
                    }
                  }}
                >
                  <Rocket sx={{ fontSize: 50, color: modernColors.primary.main }} />
                </Box>
              </Zoom>

              <Typography
                variant="h2"
                sx={{
                  fontWeight: 900,
                  background: modernColors.gradients.primary,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 2,
                  fontSize: { xs: '2rem', md: '2.5rem' },
                  letterSpacing: '-0.02em',
                }}
              >
                Join the Mining Revolution
              </Typography>

              <Typography variant="h6" sx={{
                color: modernColors.dark.text.secondary,
                maxWidth: 500,
                mx: 'auto',
                lineHeight: 1.7,
                fontSize: '1.1rem',
                fontWeight: 400,
              }}>
                Start your journey in decentralized token mining. Connect, register, and begin earning rewards immediately.
              </Typography>
            </Box>

            {/* Rewards Info */}
            <Box sx={{ mb: 5 }}>
              <Typography variant="h5" sx={{
                textAlign: 'center',
                mb: 3,
                fontWeight: 600,
                color: modernColors.dark.text.primary,
              }}>
                🎁 Exclusive Rewards Await
              </Typography>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                  gap: 3,
                  mb: 3,
                }}
              >
                <Zoom in timeout={1200}>
                  <Card
                    sx={{
                      background: `linear-gradient(135deg, ${modernColors.secondary.main}15, ${modernColors.secondary.dark}15)`,
                      border: `2px solid ${modernColors.secondary.main}30`,
                      borderRadius: 3,
                      p: 3,
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '3px',
                        background: `linear-gradient(90deg, ${modernColors.secondary.main}, ${modernColors.secondary.light})`,
                      },
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 12px 24px ${modernColors.secondary.main}20`,
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Diamond sx={{ color: modernColors.secondary.main, mr: 1.5, fontSize: 28 }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, color: modernColors.dark.text.primary }}>
                        Registration Bonus
                      </Typography>
                    </Box>
                    <Typography variant="h4" sx={{
                      fontWeight: 800,
                      color: modernColors.secondary.main,
                      mb: 1,
                    }}>
                      {formatAmount(regReward)} Tokens
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Instant reward upon registration
                    </Typography>
                  </Card>
                </Zoom>

                <Zoom in timeout={1400}>
                  <Card
                    sx={{
                      background: `linear-gradient(135deg, ${modernColors.accent.gold}15, ${modernColors.accent.orange}15)`,
                      border: `2px solid ${modernColors.accent.gold}30`,
                      borderRadius: 3,
                      p: 3,
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '3px',
                        background: `linear-gradient(90deg, ${modernColors.accent.gold}, ${modernColors.accent.orange})`,
                      },
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 12px 24px ${modernColors.accent.gold}20`,
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PersonAdd sx={{ color: modernColors.accent.gold, mr: 1.5, fontSize: 28 }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, color: modernColors.dark.text.primary }}>
                        Daily Mining Rewards
                      </Typography>
                    </Box>
                    <Typography variant="h4" sx={{
                      fontWeight: 800,
                      color: modernColors.accent.gold,
                      mb: 1,
                    }}>
                      {formatAmount(dailyReward)} Tokens
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Earn every 24 hours
                    </Typography>
                  </Card>
                </Zoom>
              </Box>
            </Box>

            <Divider sx={{
              mb: 4,
              borderColor: modernColors.dark.border.secondary,
              '&::before, &::after': {
                borderColor: modernColors.dark.border.secondary,
              }
            }} />

            {/* Referrer Input */}
            <Box sx={{ mb: 5 }}>
              <Typography variant="h5" sx={{
                mb: 3,
                display: 'flex',
                alignItems: 'center',
                fontWeight: 600,
                color: modernColors.dark.text.primary,
              }}>
                <LinkIcon sx={{ mr: 1.5, color: modernColors.primary.main, fontSize: 28 }} />
                Referrer Address (Optional)
              </Typography>

              <Typography variant="body1" sx={{
                mb: 3,
                color: modernColors.dark.text.secondary,
                lineHeight: 1.6,
              }}>
                Have a referrer? Enter their wallet address to earn additional bonuses and join their network.
              </Typography>

              <TextField
                fullWidth
                label="Enter referrer wallet address"
                value={referrerAddress}
                onChange={(e) => setReferrerAddress(e.target.value)}
                placeholder="0x1234567890abcdef..."
                disabled={registering}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: `${modernColors.dark.surface.secondary}40`,
                    border: `2px solid ${modernColors.dark.border.primary}`,
                    '&:hover': {
                      borderColor: modernColors.primary.main,
                    },
                    '&.Mui-focused': {
                      borderColor: modernColors.primary.main,
                      boxShadow: `0 0 0 3px ${modernColors.primary.main}20`,
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: modernColors.dark.text.secondary,
                    '&.Mui-focused': {
                      color: modernColors.primary.main,
                    },
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LinkIcon sx={{ color: modernColors.primary.main }} />
                    </InputAdornment>
                  ),
                  endAdornment: referrerAddress && (
                    <InputAdornment position="end">
                      {isValidAddress ? (
                        <Tooltip title="Valid Ethereum address">
                          <CheckCircle sx={{ color: modernColors.success, fontSize: 24 }} />
                        </Tooltip>
                      ) : (
                        <Tooltip title="Invalid address format">
                          <IconButton onClick={copyAddress} size="small">
                            <ContentCopy sx={{ color: modernColors.warning }} />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  ),
                }}
              />

              {referrerAddress && (
                <Fade in>
                  <Alert
                    severity={isValidAddress ? "success" : "warning"}
                    sx={{ mb: 2 }}
                    icon={isValidAddress ? <CheckCircle /> : <Info />}
                  >
                    {isValidAddress
                      ? "✅ Valid referrer address - they will earn commissions from your activity"
                      : "⚠️ Please enter a valid Ethereum address (0x...)"
                    }
                  </Alert>
                </Fade>
              )}

              {!referrerAddress && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    💡 Leave empty to register without a referrer. You can still earn from your own referrals!
                  </Typography>
                </Alert>
              )}

              {copied && (
                <Chip
                  label="Address copied!"
                  color="success"
                  size="small"
                  sx={{ mb: 1 }}
                />
              )}
            </Box>

            {/* Register Button */}
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handleRegister}
              disabled={registering || (referrerAddress && !isValidAddress)}
              startIcon={registering ? <CircularProgress size={20} /> : <Rocket />}
              sx={{
                py: 2,
                fontSize: '1.1rem',
                fontWeight: 700,
                background: modernColors.gradients.primary,
                '&:hover': {
                  background: modernColors.gradients.primary,
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(99, 102, 241, 0.4)',
                },
                '&:disabled': {
                  background: modernColors.dark.surface.secondary,
                  color: modernColors.dark.text.tertiary,
                },
              }}
            >
              {registering ? 'Registering...' : 'Start Mining Journey'}
            </Button>

            {showSuccess && (
              <Fade in>
                <Alert severity="success" sx={{ mt: 2 }}>
                  🎉 Registration successful! Welcome to the mining platform!
                </Alert>
              </Fade>
            )}
          </CardContent>
        </Card>
      </Fade>
    </Box>
  );
};

export default ModernRegistration;
