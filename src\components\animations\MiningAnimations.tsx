import React, { useEffect, useState } from 'react';
import { Box, Typography, keyframes } from '@mui/material';
import { modernColors } from '../../theme/modernTheme';

interface MiningAnimationsProps {
  isActive: boolean;
  intensity?: 'low' | 'medium' | 'high';
  showParticles?: boolean;
}

// Keyframe animations
const miningPulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
`;

const particleFloat = keyframes`
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(20px) rotate(360deg);
    opacity: 0;
  }
`;

const energyFlow = keyframes`
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
`;

const glowPulse = keyframes`
  0% {
    box-shadow: 0 0 5px ${modernColors.primary.main}40;
  }
  50% {
    box-shadow: 0 0 20px ${modernColors.primary.main}80, 0 0 30px ${modernColors.secondary.main}60;
  }
  100% {
    box-shadow: 0 0 5px ${modernColors.primary.main}40;
  }
`;

const rotateGear = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const MiningAnimations: React.FC<MiningAnimationsProps> = ({
  isActive,
  intensity = 'medium',
  showParticles = true,
}) => {
  const [particles, setParticles] = useState<Array<{ id: number; delay: number; size: number }>>([]);

  useEffect(() => {
    if (isActive && showParticles) {
      const particleCount = intensity === 'high' ? 15 : intensity === 'medium' ? 10 : 5;
      const newParticles = Array.from({ length: particleCount }, (_, i) => ({
        id: i,
        delay: Math.random() * 3,
        size: Math.random() * 8 + 4,
      }));
      setParticles(newParticles);
    } else {
      setParticles([]);
    }
  }, [isActive, intensity, showParticles]);

  const getAnimationDuration = () => {
    switch (intensity) {
      case 'high': return '1s';
      case 'medium': return '2s';
      case 'low': return '3s';
      default: return '2s';
    }
  };

  if (!isActive) {
    return null;
  }

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        pointerEvents: 'none',
      }}
    >
      {/* Central Mining Core */}
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 80,
          height: 80,
          borderRadius: '50%',
          background: modernColors.gradients.primary,
          animation: `${miningPulse} ${getAnimationDuration()} ease-in-out infinite`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        <Typography
          variant="h4"
          sx={{
            color: 'white',
            fontWeight: 700,
            textShadow: '0 0 10px rgba(255,255,255,0.5)',
          }}
        >
          ⚡
        </Typography>
      </Box>

      {/* Rotating Gears */}
      <Box
        sx={{
          position: 'absolute',
          top: '30%',
          left: '20%',
          width: 40,
          height: 40,
          borderRadius: '50%',
          border: `3px solid ${modernColors.accent.gold}`,
          animation: `${rotateGear} 4s linear infinite`,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 8,
            height: 8,
            borderRadius: '50%',
            background: modernColors.accent.gold,
          },
        }}
      />

      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          right: '25%',
          width: 30,
          height: 30,
          borderRadius: '50%',
          border: `2px solid ${modernColors.accent.cyan}`,
          animation: `${rotateGear} 3s linear infinite reverse`,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 6,
            height: 6,
            borderRadius: '50%',
            background: modernColors.accent.cyan,
          },
        }}
      />

      {/* Energy Flow Lines */}
      <Box
        sx={{
          position: 'absolute',
          top: '45%',
          left: 0,
          right: 0,
          height: 2,
          background: `linear-gradient(90deg, transparent, ${modernColors.primary.main}, transparent)`,
          animation: `${energyFlow} 2s ease-in-out infinite`,
        }}
      />

      <Box
        sx={{
          position: 'absolute',
          top: '55%',
          left: 0,
          right: 0,
          height: 1,
          background: `linear-gradient(90deg, transparent, ${modernColors.secondary.main}, transparent)`,
          animation: `${energyFlow} 3s ease-in-out infinite`,
          animationDelay: '1s',
        }}
      />

      {/* Floating Particles */}
      {particles.map((particle) => (
        <Box
          key={particle.id}
          sx={{
            position: 'absolute',
            bottom: '20%',
            left: `${Math.random() * 80 + 10}%`,
            width: particle.size,
            height: particle.size,
            borderRadius: '50%',
            background: Math.random() > 0.5 ? modernColors.accent.gold : modernColors.accent.cyan,
            animation: `${particleFloat} 4s ease-out infinite`,
            animationDelay: `${particle.delay}s`,
            boxShadow: `0 0 10px ${Math.random() > 0.5 ? modernColors.accent.gold : modernColors.accent.cyan}`,
          }}
        />
      ))}

      {/* Mining Status Indicators */}
      <Box
        sx={{
          position: 'absolute',
          top: 20,
          right: 20,
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
        }}
      >
        <Box
          sx={{
            width: 12,
            height: 12,
            borderRadius: '50%',
            background: modernColors.success,
            animation: `${miningPulse} 1s ease-in-out infinite`,
            boxShadow: `0 0 10px ${modernColors.success}`,
          }}
        />
        <Box
          sx={{
            width: 10,
            height: 10,
            borderRadius: '50%',
            background: modernColors.accent.gold,
            animation: `${miningPulse} 1.5s ease-in-out infinite`,
            animationDelay: '0.5s',
            boxShadow: `0 0 8px ${modernColors.accent.gold}`,
          }}
        />
        <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            background: modernColors.accent.cyan,
            animation: `${miningPulse} 2s ease-in-out infinite`,
            animationDelay: '1s',
            boxShadow: `0 0 6px ${modernColors.accent.cyan}`,
          }}
        />
      </Box>

      {/* Hash Rate Visualization */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 20,
          left: 20,
          display: 'flex',
          gap: 2,
        }}
      >
        {Array.from({ length: 5 }).map((_, i) => (
          <Box
            key={i}
            sx={{
              width: 4,
              height: Math.random() * 30 + 10,
              background: modernColors.gradients.primary,
              borderRadius: 2,
              animation: `${miningPulse} ${1 + Math.random()}s ease-in-out infinite`,
              animationDelay: `${i * 0.2}s`,
            }}
          />
        ))}
      </Box>

      {/* Glow Effect Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at center, ${modernColors.primary.main}10 0%, transparent 70%)`,
          animation: `${glowPulse} 3s ease-in-out infinite`,
          pointerEvents: 'none',
        }}
      />

      {/* Mining Text Effect */}
      <Box
        sx={{
          position: 'absolute',
          bottom: '10%',
          left: '50%',
          transform: 'translateX(-50%)',
          textAlign: 'center',
        }}
      >
        <Typography
          variant="body2"
          sx={{
            color: modernColors.primary.main,
            fontWeight: 600,
            textShadow: `0 0 10px ${modernColors.primary.main}`,
            animation: `${miningPulse} 2s ease-in-out infinite`,
          }}
        >
          ⛏️ MINING ACTIVE ⛏️
        </Typography>
      </Box>
    </Box>
  );
};

export default MiningAnimations;
