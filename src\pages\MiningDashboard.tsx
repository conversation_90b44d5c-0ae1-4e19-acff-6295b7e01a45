import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Paper,
  Chip,
  Stack,
  IconButton,
  CircularProgress,
  LinearProgress,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  AccountBalanceWallet,
  People,
  MonetizationOn,
  Refresh,
  CheckCircle,
  PlayArrow,
  Pause,
  TrendingUp,
  EmojiEvents,
} from '@mui/icons-material';
import { ConnectButton } from "thirdweb/react";
import { client, bscTestnet, MINING_CONTRACT_ADDRESS } from '../client';
import { createWallet } from "thirdweb/wallets";
import { useMining } from '../context/MiningContext';
import SetupInstructions from '../components/SetupInstructions';

const MiningDashboard: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const {
    address,
    isConnected,
    isRegistered,
    isLoading,
    userRecord,
    dailyReward,
    regReward,
    directReferralCount,
    totalRegistered,
    canClaim,
    timeUntilNextClaim,
    register,
    claimDailyReward,
    refreshData,
    isCorrectNetwork,
    switchToCorrectNetwork,
  } = useMining();

  const [refreshing, setRefreshing] = useState(false);
  const [claiming, setClaiming] = useState(false);
  const [registering, setRegistering] = useState(false);
  const [miningAnimation, setMiningAnimation] = useState(false);

  // Define wallets for ThirdWeb
  const wallets = [
    createWallet("io.metamask"),
    createWallet("com.coinbase.wallet"),
    createWallet("com.trustwallet.app"),
    createWallet("walletConnect"),
    createWallet("io.rabby"),
    createWallet("io.zerion.wallet"),
  ];

  // Format BigInt to readable string
  const formatAmount = (amount: bigint, decimals: number = 18): string => {
    const divisor = BigInt(10 ** decimals);
    const quotient = amount / divisor;
    const remainder = amount % divisor;

    if (remainder === 0n) {
      return quotient.toString();
    }

    const remainderStr = remainder.toString().padStart(decimals, '0');
    const trimmedRemainder = remainderStr.replace(/0+$/, '');

    if (trimmedRemainder === '') {
      return quotient.toString();
    }

    return `${quotient}.${trimmedRemainder}`;
  };

  // Format time countdown
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Calculate user mining rank based on total mined
  const getUserMiningRank = () => {
    if (!userRecord || userRecord.totalMinted === 0n) {
      return { rank: 'New Miner', icon: '⛏️', progress: 0, next: 'Start Mining', hasMined: false };
    }

    const totalMined = Number(formatAmount(userRecord.totalMinted));

    if (totalMined >= 1000) return { rank: 'Diamond Miner', icon: '💎', progress: 100, next: 'Max Level', hasMined: true };
    if (totalMined >= 500) return { rank: 'Gold Miner', icon: '🏆', progress: 80, next: 'Diamond Miner', hasMined: true };
    if (totalMined >= 100) return { rank: 'Silver Miner', icon: '🥈', progress: 60, next: 'Gold Miner', hasMined: true };
    if (totalMined >= 50) return { rank: 'Bronze Miner', icon: '🥉', progress: 40, next: 'Silver Miner', hasMined: true };

    return { rank: 'Starter Miner', icon: '⭐', progress: 20, next: 'Bronze Miner', hasMined: true };
  };

  const userMiningRank = getUserMiningRank();

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleRegister = async () => {
    setRegistering(true);
    try {
      const success = await register();
      if (success) {
        console.log('Registration successful!');
      }
    } catch (error) {
      console.error('Registration failed:', error);
    } finally {
      setRegistering(false);
    }
  };

  const handleClaim = async () => {
    setClaiming(true);
    setMiningAnimation(true);
    try {
      const success = await claimDailyReward();
      if (success) {
        console.log('Claim successful!');
      }
    } catch (error) {
      console.error('Claim failed:', error);
    } finally {
      setClaiming(false);
      setTimeout(() => setMiningAnimation(false), 2000);
    }
  };

  const handleSwitchNetwork = async () => {
    try {
      await switchToCorrectNetwork();
    } catch (error) {
      console.error('Network switch failed:', error);
    }
  };

  // Check if contract addresses are properly configured
  const isContractConfigured = MINING_CONTRACT_ADDRESS !== "0x1234567890123456789012345678901234567890";

  // Mining animation effect
  useEffect(() => {
    if (isRegistered && canClaim) {
      const interval = setInterval(() => {
        setMiningAnimation(prev => !prev);
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [isRegistered, canClaim]);

  // Show setup instructions if contract not configured
  if (!isContractConfigured) {
    return <SetupInstructions />;
  }

  // Show connection UI if not connected
  if (!isConnected) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{ p: 4, background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)', color: 'white' }}>
          <CardContent>
            <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
              🚀 Token Mining Platform
            </Typography>
            <Typography variant="h6" sx={{ mb: 4, color: '#b0b0b0' }}>
              Connect your wallet to start mining tokens daily
            </Typography>
            <ConnectButton
              client={client}
              wallets={wallets}
              chain={bscTestnet}
              connectModal={{
                size: "wide",
                title: "Connect to Mining Platform",
                showThirdwebBranding: false,
              }}
            />
          </CardContent>
        </Card>
      </Container>
    );
  }

  // Show network switch UI if wrong network
  if (!isCorrectNetwork) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{ p: 4, background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)', color: 'white' }}>
          <CardContent>
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
              ⚠️ Wrong Network
            </Typography>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Please switch to BSC Testnet to continue
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={handleSwitchNetwork}
              sx={{ bgcolor: 'white', color: '#ff9800', '&:hover': { bgcolor: '#f5f5f5' } }}
            >
              Switch to BSC Testnet
            </Button>
          </CardContent>
        </Card>
      </Container>
    );
  }

  // Show registration UI if not registered
  if (!isRegistered) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{ p: 4, background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)', color: 'white' }}>
          <CardContent>
            <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold' }}>
              🎯 Join Mining Platform
            </Typography>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Register to start earning {formatAmount(regReward)} tokens immediately
            </Typography>
            <Typography variant="body1" sx={{ mb: 4, opacity: 0.9 }}>
              Daily rewards: {formatAmount(dailyReward)} tokens
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={handleRegister}
              disabled={registering}
              sx={{ 
                bgcolor: 'white', 
                color: '#4caf50', 
                px: 4, 
                py: 1.5,
                '&:hover': { bgcolor: '#f5f5f5' } 
              }}
            >
              {registering ? <CircularProgress size={24} /> : 'Register Now'}
            </Button>
          </CardContent>
        </Card>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      {/* Welcome Header - Matching MLM Theme */}
      <Box sx={{
        background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
        borderRadius: 2,
        p: { xs: 1.5, sm: 2, md: 2.5 },
        mb: { xs: 1.5, sm: 2 },
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.3)',
        border: '1px solid #333',
      }}>
        {/* Subtle Background Elements */}
        <Box sx={{
          position: 'absolute',
          top: -30,
          right: -30,
          width: 120,
          height: 120,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.02)',
        }} />
        <Box sx={{
          position: 'absolute',
          bottom: -20,
          left: -20,
          width: 100,
          height: 100,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.01)',
        }} />

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: { xs: 1, md: 2 }, alignItems: 'center', position: 'relative', zIndex: 1 }}>
          <Box sx={{ flex: 1, width: '100%' }}>
            <Typography variant="h3" gutterBottom sx={{
              fontWeight: 'bold',
              color: '#ffffff',
              mb: { xs: 0.5, sm: 1 },
              fontSize: { xs: '1.4rem', sm: '1.6rem', md: '2rem' }
            }}>
              {userMiningRank.hasMined ? `Welcome Back, ${userMiningRank.rank}!` : 'Welcome to Token Mining!'}
            </Typography>

            <Typography variant="h6" sx={{
              color: '#b0b0b0',
              mb: { xs: 1.5, sm: 2 },
              fontWeight: 400,
              lineHeight: 1.2,
              fontSize: { xs: '0.75rem', sm: '0.85rem', md: '1rem' }
            }}>
              Mine tokens daily and earn through referrals
            </Typography>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={{ xs: 0.5, sm: 1 }}
              flexWrap="wrap"
              sx={{
                mb: { xs: 1.5, sm: 2 },
                '& .MuiChip-root': {
                  fontSize: { xs: '0.65rem', sm: '0.75rem' },
                  height: { xs: 24, sm: 28 }
                }
              }}
            >
              <Chip
                icon={<CheckCircle />}
                label="BSC Testnet"
                sx={{
                  bgcolor: '#1e3a1e',
                  color: '#4caf50',
                  fontWeight: 'bold',
                  border: '1px solid #4caf50'
                }}
              />
              <Chip
                icon={<CheckCircle />}
                label={userMiningRank.hasMined ? "Active Miner" : "Platform Member"}
                sx={{
                  bgcolor: userMiningRank.hasMined ? '#1e3a1e' : '#2e2e1e',
                  color: userMiningRank.hasMined ? '#4caf50' : '#ff9800',
                  fontWeight: 'bold',
                  border: `1px solid ${userMiningRank.hasMined ? '#4caf50' : '#ff9800'}`
                }}
              />
              <Chip
                icon={<AccountBalanceWallet />}
                label={`${userRecord ? formatAmount(userRecord.totalMinted) : '0'} Tokens`}
                sx={{
                  bgcolor: '#2d2d2d',
                  color: '#ffffff',
                  fontWeight: 'bold',
                  border: '1px solid #555'
                }}
              />
            </Stack>

            <Typography variant="body2" sx={{
              color: '#888',
              fontFamily: 'monospace',
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              wordBreak: 'break-all'
            }}>
              Wallet: {address ? `${address.slice(0, isMobile ? 8 : 12)}...${address.slice(isMobile ? -6 : -10)}` : 'Not connected'}
            </Typography>
          </Box>

          <Box sx={{
            minWidth: { xs: '100%', md: 280 },
            width: { xs: '100%', md: 'auto' }
          }}>
            <Paper sx={{
                p: { xs: 1.5, sm: 2, md: 2.5 },
                background: '#1e1e1e',
                borderRadius: 2,
                textAlign: 'center',
                border: '1px solid #333',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 6px 20px rgba(0, 0, 0, 0.4)',
                  borderColor: '#444',
                }
              }}>
                <Typography variant="h2" sx={{
                  fontWeight: 'bold',
                  mb: 0.5,
                  fontSize: { xs: '1.75rem', sm: '2rem' },
                  color: '#ffffff',
                }}>
                  {userMiningRank.icon}
                </Typography>

                <Typography variant="h5" sx={{
                  fontWeight: 'bold',
                  mb: 0.5,
                  color: '#ffffff',
                  fontSize: { xs: '1.1rem', sm: '1.25rem' }
                }}>
                  {userMiningRank.rank}
                </Typography>

                <Typography variant="body2" sx={{
                  mb: { xs: 1.5, sm: 2 },
                  color: '#b0b0b0',
                  fontSize: { xs: '0.75rem', sm: '0.8rem' }
                }}>
                  {userMiningRank.hasMined ? 'Your Mining Tier' : 'Ready to Start Mining'}
                </Typography>

                <Box sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: { xs: 0.5, sm: 1 },
                  pt: { xs: 1, sm: 1.5 },
                  borderTop: '1px solid #333'
                }}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 'bold',
                      color: '#4caf50',
                      fontSize: { xs: '1rem', sm: '1.25rem' }
                    }}>
                      {userRecord ? formatAmount(userRecord.totalMinted).slice(0, 6) : '0'}
                    </Typography>
                    <Typography variant="caption" sx={{
                      color: '#888',
                      fontSize: { xs: '0.7rem', sm: '0.75rem' }
                    }}>
                      Mined
                    </Typography>
                  </Box>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 'bold',
                      color: '#2196f3',
                      fontSize: { xs: '1rem', sm: '1.25rem' }
                    }}>
                      {directReferralCount}
                    </Typography>
                    <Typography variant="caption" sx={{
                      color: '#888',
                      fontSize: { xs: '0.7rem', sm: '0.75rem' }
                    }}>
                      Referrals
                    </Typography>
                  </Box>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 'bold',
                      color: '#ff9800',
                      fontSize: { xs: '1rem', sm: '1.25rem' }
                    }}>
                      {userRecord ? formatAmount(userRecord.boosterIncome).slice(0, 6) : '0'}
                    </Typography>
                    <Typography variant="caption" sx={{
                      color: '#888',
                      fontSize: { xs: '0.7rem', sm: '0.75rem' }
                    }}>
                      Booster
                    </Typography>
                  </Box>
                </Box>
              </Paper>
          </Box>
        </Box>

        {/* Refresh Button */}
        <IconButton
          onClick={handleRefresh}
          disabled={refreshing}
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            bgcolor: '#333',
            color: '#ffffff',
            border: '1px solid #555',
            '&:hover': {
              bgcolor: '#444',
            }
          }}
        >
          <Refresh sx={{
            animation: refreshing ? 'spin 1s linear infinite' : 'none',
            '@keyframes spin': {
              '0%': { transform: 'rotate(0deg)' },
              '100%': { transform: 'rotate(360deg)' },
            }
          }} />
        </IconButton>
      </Box>

      {/* Real-time Stats Cards */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr',
          sm: 'repeat(2, 1fr)',
          md: 'repeat(4, 1fr)'
        },
        gap: { xs: 2, sm: 3 },
        mb: 4
      }}>
        <Card sx={{
          background: '#1e1e1e',
          color: 'white',
          border: '1px solid #333',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4)',
            borderColor: '#4caf50'
          }
        }}>
          <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="h6" color="#ffffff" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                Total Mined
              </Typography>
              <MonetizationOn sx={{ color: '#4caf50', fontSize: { xs: 24, sm: 30 } }} />
            </Box>
            <Typography variant="h4" sx={{
              fontWeight: 'bold',
              mb: 1,
              color: '#ffffff',
              fontSize: { xs: '1.5rem', sm: '2.125rem' }
            }}>
              {userRecord ? formatAmount(userRecord.totalMinted) : '0'}
            </Typography>
            <Typography variant="body2" sx={{ color: '#b0b0b0' }}>Tokens</Typography>
            <Typography variant="caption" sx={{ color: '#888', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
              Daily Reward: {formatAmount(dailyReward)} Tokens
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{
          background: '#1e1e1e',
          color: 'white',
          border: '1px solid #333',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4)',
            borderColor: '#ff9800'
          }
        }}>
          <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="h6" color="#ffffff" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                Booster Income
              </Typography>
              <TrendingUp sx={{ color: '#ff9800', fontSize: { xs: 24, sm: 30 } }} />
            </Box>
            <Typography variant="h4" sx={{
              fontWeight: 'bold',
              mb: 1,
              color: '#ffffff',
              fontSize: { xs: '1.5rem', sm: '2.125rem' }
            }}>
              {userRecord ? formatAmount(userRecord.boosterIncome) : '0'}
            </Typography>
            <Typography variant="body2" sx={{ color: '#b0b0b0' }}>Tokens</Typography>
            <Typography variant="caption" sx={{ color: '#888', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
              From Referral Bonuses
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{
          background: '#1e1e1e',
          color: 'white',
          border: '1px solid #333',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4)',
            borderColor: '#2196f3'
          }
        }}>
          <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="h6" color="#ffffff" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                My Referrals
              </Typography>
              <People sx={{ color: '#2196f3', fontSize: { xs: 24, sm: 30 } }} />
            </Box>
            <Typography variant="h4" sx={{
              fontWeight: 'bold',
              mb: 1,
              color: '#ffffff',
              fontSize: { xs: '1.5rem', sm: '2.125rem' }
            }}>
              {directReferralCount}
            </Typography>
            <Typography variant="body2" sx={{ color: '#b0b0b0' }}>Direct Referrals</Typography>
            <Typography variant="caption" sx={{ color: '#888', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
              Earn from their mining
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{
          background: miningAnimation
            ? 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)'
            : '#1e1e1e',
          color: 'white',
          border: miningAnimation ? '2px solid #4caf50' : '1px solid #333',
          transition: 'all 0.5s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4)',
            borderColor: canClaim ? '#4caf50' : '#666'
          }
        }}>
          <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="h6" color="#ffffff" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                Mining Status
              </Typography>
              <Box sx={{
                animation: miningAnimation ? 'pulse 1s infinite' : 'none',
                '@keyframes pulse': {
                  '0%': { transform: 'scale(1)' },
                  '50%': { transform: 'scale(1.1)' },
                  '100%': { transform: 'scale(1)' },
                }
              }}>
                {canClaim ? <PlayArrow sx={{ color: '#4caf50', fontSize: { xs: 24, sm: 30 } }} /> : <Pause sx={{ color: '#666', fontSize: { xs: 24, sm: 30 } }} />}
              </Box>
            </Box>
            <Typography variant="h4" sx={{
              fontWeight: 'bold',
              mb: 1,
              color: '#ffffff',
              fontSize: { xs: '1.5rem', sm: '2.125rem' }
            }}>
              {canClaim ? 'Ready!' : formatTime(timeUntilNextClaim)}
            </Typography>
            <Typography variant="body2" sx={{ color: '#b0b0b0' }}>
              {canClaim ? 'Claim Available' : 'Next Claim'}
            </Typography>

            {!canClaim && (
              <Box sx={{ mt: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={((120 - timeUntilNextClaim) / 120) * 100}
                  sx={{
                    height: 4,
                    borderRadius: 2,
                    bgcolor: '#333',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: '#4caf50',
                      borderRadius: 2,
                    }
                  }}
                />
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>

      {/* Mining Action Card */}
      <Card sx={{
        background: 'linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%)',
        color: 'white',
        border: '1px solid #333',
        mb: 4
      }}>
        <CardContent sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 2 }}>
            ⛏️ Daily Mining Claim
          </Typography>
          <Typography variant="h6" sx={{ color: '#b0b0b0', mb: 3 }}>
            {canClaim ? 'Your daily reward is ready to claim!' : `Next claim available in ${formatTime(timeUntilNextClaim)}`}
          </Typography>

          <Button
            variant="contained"
            size="large"
            onClick={handleClaim}
            disabled={!canClaim || claiming}
            startIcon={claiming ? <CircularProgress size={20} /> : canClaim ? <PlayArrow /> : <Pause />}
            sx={{
              py: 2,
              px: 4,
              fontSize: '1.2rem',
              fontWeight: 'bold',
              bgcolor: canClaim ? '#4caf50' : '#666',
              minWidth: 200,
              '&:hover': {
                bgcolor: canClaim ? '#388e3c' : '#666',
              }
            }}
          >
            {claiming ? 'Claiming...' : canClaim ? `Claim ${formatAmount(dailyReward)} Tokens` : 'Mining in Progress'}
          </Button>
        </CardContent>
      </Card>
    </Container>
  );
};

export default MiningDashboard;
