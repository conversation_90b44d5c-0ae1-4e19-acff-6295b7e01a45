import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom,
} from '@mui/material';
import {
  AccountBalanceWallet,
  TrendingUp,
  Group,
} from '@mui/icons-material';
import { ConnectButton } from "thirdweb/react";
import { client, bscTestnet } from '../client';
import { useMining } from '../context/MiningContext';
import ModernRegistration from '../components/registration/ModernRegistration';
import DashboardLayout from '../components/layout/DashboardLayout';
import MiningStatusCard from '../components/mining/MiningStatusCard';
import ReferralsPage from './ReferralsPage';
import { modernColors } from '../theme/modernTheme';

const MiningDashboard: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const {
    address,
    isConnected,
    isRegistered,
    isLoading,
    userRecord,
    dailyReward,
    regReward,
    directReferralCount,
    totalRegistered,
    canClaim,
    timeUntilNextClaim,
    register,
    claimDailyReward,
    refreshData,
    isCorrectNetwork,
    switchToCorrectNetwork,
  } = useMining();

  const [registering, setRegistering] = useState(false);
  const [claiming, setClaiming] = useState(false);
  const [currentPage, setCurrentPage] = useState('dashboard');

  // Format BigInt to readable string
  const formatAmount = (amount: bigint, decimals: number = 18): string => {
    const divisor = BigInt(10 ** decimals);
    const quotient = amount / divisor;
    const remainder = amount % divisor;

    if (remainder === 0n) {
      return quotient.toString();
    }

    const remainderStr = remainder.toString().padStart(decimals, '0');
    const trimmedRemainder = remainderStr.replace(/0+$/, '');

    if (trimmedRemainder === '') {
      return quotient.toString();
    }

    return `${quotient}.${trimmedRemainder}`;
  };

  const handleRegister = async (referrerAddress?: string) => {
    setRegistering(true);
    try {
      const success = await register(referrerAddress);
      if (success) {
        console.log('Registration successful!');
      }
      return success;
    } catch (error) {
      console.error('Registration failed:', error);
      return false;
    } finally {
      setRegistering(false);
    }
  };

  const handleClaim = async () => {
    setClaiming(true);
    try {
      const success = await claimDailyReward();
      if (success) {
        console.log('Claim successful!');
      }
    } catch (error) {
      console.error('Claim failed:', error);
    } finally {
      setClaiming(false);
    }
  };

  const handlePageChange = (page: string) => {
    setCurrentPage(page);
  };

  const handleSwitchNetwork = async () => {
    try {
      await switchToCorrectNetwork();
    } catch (error) {
      console.error('Network switch failed:', error);
    }
  };

  // Show connection UI if not connected
  if (!isConnected) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{
          p: 4,
          background: modernColors.gradients.card,
          border: `1px solid ${modernColors.dark.border.primary}`,
          borderRadius: 3,
        }}>
          <CardContent>
            <Typography variant="h3" gutterBottom sx={{
              fontWeight: 'bold',
              mb: 3,
              background: modernColors.gradients.primary,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}>
              🚀 Token Mining Platform
            </Typography>
            <Typography variant="h6" sx={{ mb: 4, color: modernColors.dark.text.secondary }}>
              Connect your wallet to start mining tokens daily
            </Typography>
            <ConnectButton
              client={client}
              chain={bscTestnet}
              connectModal={{
                size: "wide",
                title: "Connect to Mining Platform",
                showThirdwebBranding: false,
              }}
            />
          </CardContent>
        </Card>
      </Container>
    );
  }

  // Show network switch UI if wrong network
  if (!isCorrectNetwork) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{
          p: 4,
          background: `linear-gradient(135deg, ${modernColors.warning}20, ${modernColors.error}20)`,
          border: `1px solid ${modernColors.warning}40`,
          borderRadius: 3,
        }}>
          <CardContent>
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: modernColors.warning }}>
              ⚠️ Wrong Network
            </Typography>
            <Typography variant="h6" sx={{ mb: 3, color: modernColors.dark.text.secondary }}>
              Please switch to BSC Testnet to continue
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={handleSwitchNetwork}
              sx={{
                background: modernColors.gradients.primary,
                '&:hover': {
                  background: modernColors.gradients.primary,
                  transform: 'translateY(-2px)',
                },
              }}
            >
              Switch to BSC Testnet
            </Button>
          </CardContent>
        </Card>
      </Container>
    );
  }

  // Show registration UI if not registered
  if (!isRegistered) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <ModernRegistration
          onRegister={handleRegister}
          registering={registering}
          dailyReward={dailyReward}
          regReward={regReward}
        />
      </Container>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: modernColors.gradients.dark,
      }}>
        <CircularProgress size={60} sx={{ color: modernColors.primary.main }} />
      </Box>
    );
  }

  // Render different pages based on currentPage
  const renderPageContent = () => {
    switch (currentPage) {
      case 'referrals':
        return <ReferralsPage />;
      case 'dashboard':
      default:
        return (
          <Container maxWidth="xl" sx={{ py: 3 }}>
            <Grid container spacing={3}>
              {/* Welcome Header */}
              <Grid item xs={12}>
                <Fade in timeout={600}>
                  <Card sx={{
                    background: modernColors.gradients.card,
                    border: `1px solid ${modernColors.dark.border.primary}`,
                    borderRadius: 3,
                    p: 3,
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
                      <Box>
                        <Typography variant="h4" sx={{
                          fontWeight: 700,
                          background: modernColors.gradients.primary,
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                          mb: 1,
                        }}>
                          Welcome Back, Miner! 👋
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          Your mining operation is running smoothly
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip
                          icon={<AccountBalanceWallet />}
                          label="BSC Testnet"
                          sx={{
                            background: `${modernColors.success}20`,
                            color: modernColors.success,
                            border: `1px solid ${modernColors.success}40`,
                          }}
                        />
                        <Chip
                          icon={<Group />}
                          label={`${directReferralCount} Referrals`}
                          sx={{
                            background: `${modernColors.secondary.main}20`,
                            color: modernColors.secondary.main,
                            border: `1px solid ${modernColors.secondary.main}40`,
                          }}
                        />
                      </Box>
                    </Box>
                  </Card>
                </Fade>
              </Grid>

              {/* Mining Status Card */}
              <Grid item xs={12} md={8}>
                <MiningStatusCard
                  onClaim={handleClaim}
                  claiming={claiming}
                />
              </Grid>

              {/* Stats Cards */}
              <Grid item xs={12} md={4}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Zoom in timeout={800}>
                      <Card sx={{
                        background: `linear-gradient(135deg, ${modernColors.secondary.main}20, ${modernColors.secondary.dark}20)`,
                        border: `1px solid ${modernColors.secondary.main}40`,
                        borderRadius: 3,
                        p: 2,
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <TrendingUp sx={{ color: modernColors.secondary.main, mr: 1 }} />
                          <Typography variant="h6" sx={{ fontWeight: 600 }}>
                            Total Earned
                          </Typography>
                        </Box>
                        <Typography variant="h4" sx={{
                          fontWeight: 700,
                          color: modernColors.secondary.main,
                          mb: 1,
                        }}>
                          {userRecord ? formatAmount(userRecord.totalMinted) : '0.00'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Tokens mined to date
                        </Typography>
                      </Card>
                    </Zoom>
                  </Grid>

                  <Grid item xs={12}>
                    <Zoom in timeout={1000}>
                      <Card sx={{
                        background: `linear-gradient(135deg, ${modernColors.accent.gold}20, ${modernColors.accent.orange}20)`,
                        border: `1px solid ${modernColors.accent.gold}40`,
                        borderRadius: 3,
                        p: 2,
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Group sx={{ color: modernColors.accent.gold, mr: 1 }} />
                          <Typography variant="h6" sx={{ fontWeight: 600 }}>
                            Network
                          </Typography>
                        </Box>
                        <Typography variant="h4" sx={{
                          fontWeight: 700,
                          color: modernColors.accent.gold,
                          mb: 1,
                        }}>
                          {directReferralCount}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Direct referrals
                        </Typography>
                      </Card>
                    </Zoom>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Container>
        );
    }
  };

  return (
    <DashboardLayout
      currentPage={currentPage}
      onPageChange={handlePageChange}
    >
      {renderPageContent()}
    </DashboardLayout>
  );
};

export default MiningDashboard;