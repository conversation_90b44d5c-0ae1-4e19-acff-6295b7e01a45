import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Paper,
  Chip,
  Stack,
  IconButton,
  CircularProgress,
  LinearProgress,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  AccountBalanceWallet,
  People,
  MonetizationOn,
  Refresh,
  Diamond,
  Star,
  Timer,
  TrendingUp,
  CheckCircle,
  PlayArrow,
  Pause,
} from '@mui/icons-material';
import { ConnectButton } from "thirdweb/react";
import { client, bscMainnet, MINING_CONTRACT_ADDRESS } from '../client';
import { createWallet } from "thirdweb/wallets";
import { useMining } from '../context/MiningContext';
import SetupInstructions from '../components/SetupInstructions';

const MiningDashboard: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const {
    address,
    isConnected,
    isRegistered,
    isLoading,
    userRecord,
    dailyReward,
    regReward,
    directReferralCount,
    totalRegistered,
    canClaim,
    timeUntilNextClaim,
    register,
    claimDailyReward,
    refreshData,
    isCorrectNetwork,
    switchToCorrectNetwork,
  } = useMining();

  const [refreshing, setRefreshing] = useState(false);
  const [claiming, setClaiming] = useState(false);
  const [registering, setRegistering] = useState(false);
  const [miningAnimation, setMiningAnimation] = useState(false);

  // Define wallets for ThirdWeb
  const wallets = [
    createWallet("io.metamask"),
    createWallet("com.coinbase.wallet"),
    createWallet("com.trustwallet.app"),
    createWallet("walletConnect"),
  ];

  // Format BigInt to readable string
  const formatAmount = (amount: bigint, decimals: number = 18): string => {
    const divisor = BigInt(10 ** decimals);
    const quotient = amount / divisor;
    const remainder = amount % divisor;
    
    if (remainder === 0n) {
      return quotient.toString();
    }
    
    const remainderStr = remainder.toString().padStart(decimals, '0');
    const trimmedRemainder = remainderStr.replace(/0+$/, '');
    
    if (trimmedRemainder === '') {
      return quotient.toString();
    }
    
    return `${quotient}.${trimmedRemainder}`;
  };

  // Format time countdown
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleRegister = async () => {
    setRegistering(true);
    try {
      const success = await register();
      if (success) {
        console.log('Registration successful!');
      }
    } catch (error) {
      console.error('Registration failed:', error);
    } finally {
      setRegistering(false);
    }
  };

  const handleClaim = async () => {
    setClaiming(true);
    setMiningAnimation(true);
    try {
      const success = await claimDailyReward();
      if (success) {
        console.log('Claim successful!');
      }
    } catch (error) {
      console.error('Claim failed:', error);
    } finally {
      setClaiming(false);
      setTimeout(() => setMiningAnimation(false), 2000);
    }
  };

  const handleSwitchNetwork = async () => {
    try {
      await switchToCorrectNetwork();
    } catch (error) {
      console.error('Network switch failed:', error);
    }
  };

  // Check if contract addresses are properly configured
  const isContractConfigured = MINING_CONTRACT_ADDRESS !== "0x1234567890123456789012345678901234567890";

  // Mining animation effect
  useEffect(() => {
    if (isRegistered && canClaim) {
      const interval = setInterval(() => {
        setMiningAnimation(prev => !prev);
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [isRegistered, canClaim]);

  // Show setup instructions if contract not configured
  if (!isContractConfigured) {
    return <SetupInstructions />;
  }

  // Show connection UI if not connected
  if (!isConnected) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{ p: 4, background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)', color: 'white' }}>
          <CardContent>
            <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
              🚀 Token Mining Platform
            </Typography>
            <Typography variant="h6" sx={{ mb: 4, color: '#b0b0b0' }}>
              Connect your wallet to start mining tokens daily
            </Typography>
            <ConnectButton
              client={client}
              wallets={wallets}
              chain={bscMainnet}
              connectModal={{
                size: "wide",
                title: "Connect to Mining Platform",
                showThirdwebBranding: false,
              }}
            />
          </CardContent>
        </Card>
      </Container>
    );
  }

  // Show network switch UI if wrong network
  if (!isCorrectNetwork) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{ p: 4, background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)', color: 'white' }}>
          <CardContent>
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
              ⚠️ Wrong Network
            </Typography>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Please switch to BSC Mainnet to continue
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={handleSwitchNetwork}
              sx={{ bgcolor: 'white', color: '#ff9800', '&:hover': { bgcolor: '#f5f5f5' } }}
            >
              Switch to BSC Mainnet
            </Button>
          </CardContent>
        </Card>
      </Container>
    );
  }

  // Show registration UI if not registered
  if (!isRegistered) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: 'center' }}>
        <Card sx={{ p: 4, background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)', color: 'white' }}>
          <CardContent>
            <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold' }}>
              🎯 Join Mining Platform
            </Typography>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Register to start earning {formatAmount(regReward)} tokens immediately
            </Typography>
            <Typography variant="body1" sx={{ mb: 4, opacity: 0.9 }}>
              Daily rewards: {formatAmount(dailyReward)} tokens
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={handleRegister}
              disabled={registering}
              sx={{ 
                bgcolor: 'white', 
                color: '#4caf50', 
                px: 4, 
                py: 1.5,
                '&:hover': { bgcolor: '#f5f5f5' } 
              }}
            >
              {registering ? <CircularProgress size={24} /> : 'Register Now'}
            </Button>
          </CardContent>
        </Card>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      {/* Header */}
      <Box sx={{
        background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
        borderRadius: 2,
        p: 3,
        mb: 3,
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h3" sx={{ fontWeight: 'bold' }}>
            ⛏️ Mining Dashboard
          </Typography>
          <IconButton
            onClick={handleRefresh}
            disabled={refreshing}
            sx={{ bgcolor: '#333', color: 'white', '&:hover': { bgcolor: '#444' } }}
          >
            <Refresh sx={{
              animation: refreshing ? 'spin 1s linear infinite' : 'none',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' },
              }
            }} />
          </IconButton>
        </Box>

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1} sx={{ mb: 2 }}>
          <Chip
            icon={<CheckCircle />}
            label="BSC Mainnet"
            sx={{ bgcolor: '#1e3a1e', color: '#4caf50', border: '1px solid #4caf50' }}
          />
          <Chip
            icon={<AccountBalanceWallet />}
            label={`${address?.slice(0, 8)}...${address?.slice(-6)}`}
            sx={{ bgcolor: '#2d2d2d', color: 'white', border: '1px solid #555' }}
          />
          <Chip
            icon={<People />}
            label={`${directReferralCount} Referrals`}
            sx={{ bgcolor: '#2d2d2d', color: 'white', border: '1px solid #555' }}
          />
        </Stack>
      </Box>

      <Grid container spacing={3}>
        {/* Mining Status Card */}
        <Grid item xs={12} md={8}>
          <Card sx={{ 
            background: miningAnimation 
              ? 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)' 
              : 'linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%)',
            color: 'white',
            transition: 'all 0.5s ease',
            border: miningAnimation ? '2px solid #4caf50' : '1px solid #333',
          }}>
            <CardContent sx={{ p: 4 }}>
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Typography variant="h2" sx={{ 
                  fontSize: '4rem', 
                  mb: 2,
                  animation: miningAnimation ? 'pulse 1s infinite' : 'none',
                  '@keyframes pulse': {
                    '0%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.1)' },
                    '100%': { transform: 'scale(1)' },
                  }
                }}>
                  ⛏️
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {miningAnimation ? 'Mining Active!' : 'Mining Status'}
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.8 }}>
                  {canClaim ? 'Ready to Claim' : `Next claim in ${formatTime(timeUntilNextClaim)}`}
                </Typography>
              </Box>

              {!canClaim && (
                <Box sx={{ mb: 3 }}>
                  <LinearProgress
                    variant="determinate"
                    value={((120 - timeUntilNextClaim) / 120) * 100}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: '#333',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: '#4caf50',
                        borderRadius: 4,
                      }
                    }}
                  />
                </Box>
              )}

              <Button
                variant="contained"
                size="large"
                fullWidth
                onClick={handleClaim}
                disabled={!canClaim || claiming}
                startIcon={claiming ? <CircularProgress size={20} /> : canClaim ? <PlayArrow /> : <Pause />}
                sx={{
                  py: 2,
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  bgcolor: canClaim ? '#4caf50' : '#666',
                  '&:hover': {
                    bgcolor: canClaim ? '#388e3c' : '#666',
                  }
                }}
              >
                {claiming ? 'Claiming...' : canClaim ? 'Claim Daily Reward' : 'Mining in Progress'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Stats Cards */}
        <Grid item xs={12} md={4}>
          <Stack spacing={2}>
            <Card sx={{ background: '#1e1e1e', color: 'white', border: '1px solid #333' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="h6">Total Mined</Typography>
                  <MonetizationOn sx={{ color: '#4caf50' }} />
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                  {userRecord ? formatAmount(userRecord.totalMinted) : '0'}
                </Typography>
                <Typography variant="body2" sx={{ color: '#b0b0b0' }}>Tokens</Typography>
              </CardContent>
            </Card>

            <Card sx={{ background: '#1e1e1e', color: 'white', border: '1px solid #333' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="h6">Booster Income</Typography>
                  <TrendingUp sx={{ color: '#ff9800' }} />
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                  {userRecord ? formatAmount(userRecord.boosterIncome) : '0'}
                </Typography>
                <Typography variant="body2" sx={{ color: '#b0b0b0' }}>Tokens</Typography>
              </CardContent>
            </Card>

            <Card sx={{ background: '#1e1e1e', color: 'white', border: '1px solid #333' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="h6">Platform Users</Typography>
                  <People sx={{ color: '#2196f3' }} />
                </Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#2196f3' }}>
                  {totalRegistered.toLocaleString()}
                </Typography>
                <Typography variant="body2" sx={{ color: '#b0b0b0' }}>Registered</Typography>
              </CardContent>
            </Card>
          </Stack>
        </Grid>
      </Grid>
    </Container>
  );
};

export default MiningDashboard;
