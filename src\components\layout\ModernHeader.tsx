import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Box,
  IconButton,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  Chip,
  Button,
  useMediaQuery,
  useTheme,
  Tooltip,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  AccountBalanceWallet,
  TrendingUp,
  Refresh,
  Settings,
  Logout,
  Diamond,
  ContentCopy,
} from '@mui/icons-material';
import { ConnectButton } from 'thirdweb/react';
import { client, bscTestnet } from '../../client';
import { modernColors } from '../../theme/modernTheme';
import { useMining } from '../../context/MiningContext';

interface ModernHeaderProps {
  onMenuClick: () => void;
  sidebarOpen: boolean;
}

const ModernHeader: React.FC<ModernHeaderProps> = ({ onMenuClick, sidebarOpen }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null);
  const [copied, setCopied] = useState(false);

  const {
    address,
    isConnected,
    userRecord,
    dailyReward,
    canClaim,
    timeUntilNextClaim,
    refreshData,
    disconnectWallet,
    directReferralCount,
  } = useMining();

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const formatAmount = (amount: bigint) => {
    return (Number(amount) / 1e18).toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    });
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getUserRank = () => {
    if (!userRecord) return 'Newcomer';
    const totalMinted = Number(userRecord.totalMinted) / 1e18;
    if (totalMinted >= 10000) return 'Diamond';
    if (totalMinted >= 5000) return 'Gold';
    if (totalMinted >= 1000) return 'Silver';
    if (totalMinted >= 100) return 'Bronze';
    return 'Newcomer';
  };

  const getRankColor = () => {
    const rank = getUserRank();
    switch (rank) {
      case 'Diamond': return modernColors.accent.cyan;
      case 'Gold': return modernColors.accent.gold;
      case 'Silver': return '#c0c0c0';
      case 'Bronze': return '#cd7f32';
      default: return modernColors.primary.main;
    }
  };

  const copyAddress = () => {
    if (address) {
      navigator.clipboard.writeText(address);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationClick = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setNotificationAnchor(null);
  };

  const handleRefresh = async () => {
    await refreshData();
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: theme.zIndex.drawer + 1,
        background: modernColors.gradients.card,
        backdropFilter: 'blur(10px)',
        borderBottom: `1px solid ${modernColors.dark.border.primary}`,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* Left Section */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {isMobile && (
            <IconButton
              edge="start"
              color="inherit"
              onClick={onMenuClick}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                background: modernColors.gradients.primary,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                display: { xs: 'none', sm: 'block' },
              }}
            >
              Mining Dashboard
            </Typography>

            {/* Quick Stats */}
            {isConnected && !isMobile && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, ml: 2 }}>
                <Chip
                  icon={<Diamond />}
                  label={getUserRank()}
                  size="small"
                  sx={{
                    background: `${getRankColor()}20`,
                    color: getRankColor(),
                    border: `1px solid ${getRankColor()}40`,
                  }}
                />
                
                {directReferralCount > 0 && (
                  <Chip
                    label={`${directReferralCount} Referrals`}
                    size="small"
                    sx={{
                      background: `${modernColors.secondary.main}20`,
                      color: modernColors.secondary.main,
                      border: `1px solid ${modernColors.secondary.main}40`,
                    }}
                  />
                )}
              </Box>
            )}
          </Box>
        </Box>

        {/* Right Section */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {isConnected ? (
            <>
              {/* Refresh Button */}
              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={handleRefresh}
                  sx={{
                    color: modernColors.primary.main,
                    '&:hover': {
                      background: `${modernColors.primary.main}20`,
                    },
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>

              {/* Notifications */}
              <Tooltip title="Notifications">
                <IconButton
                  onClick={handleNotificationClick}
                  sx={{
                    color: modernColors.primary.main,
                    '&:hover': {
                      background: `${modernColors.primary.main}20`,
                    },
                  }}
                >
                  <Badge
                    badgeContent={canClaim ? 1 : 0}
                    color="error"
                    variant="dot"
                  >
                    <Notifications />
                  </Badge>
                </IconButton>
              </Tooltip>

              {/* User Profile */}
              <Tooltip title="Profile">
                <IconButton onClick={handleProfileClick}>
                  <Avatar
                    sx={{
                      width: 36,
                      height: 36,
                      background: modernColors.gradients.primary,
                      border: `2px solid ${getRankColor()}`,
                    }}
                  >
                    <Diamond />
                  </Avatar>
                </IconButton>
              </Tooltip>
            </>
          ) : (
            <ConnectButton
              client={client}
              chain={bscTestnet}
              theme="dark"
              connectButton={{
                style: {
                  background: modernColors.gradients.primary,
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontWeight: 600,
                },
              }}
            />
          )}
        </Box>
      </Toolbar>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            background: modernColors.gradients.card,
            border: `1px solid ${modernColors.dark.border.primary}`,
            borderRadius: 2,
            minWidth: 280,
            mt: 1,
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar
              sx={{
                width: 48,
                height: 48,
                background: modernColors.gradients.primary,
                border: `2px solid ${getRankColor()}`,
              }}
            >
              <Diamond />
            </Avatar>
            <Box>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                {address ? formatAddress(address) : 'Not Connected'}
              </Typography>
              <Chip
                label={getUserRank()}
                size="small"
                sx={{
                  background: `${getRankColor()}20`,
                  color: getRankColor(),
                  border: `1px solid ${getRankColor()}40`,
                  fontSize: '0.75rem',
                }}
              />
            </Box>
          </Box>

          {address && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                p: 1,
                background: modernColors.dark.surface.secondary,
                borderRadius: 1,
                mb: 2,
              }}
            >
              <Typography variant="body2" sx={{ fontFamily: 'monospace', flex: 1 }}>
                {address}
              </Typography>
              <IconButton size="small" onClick={copyAddress}>
                <ContentCopy fontSize="small" />
              </IconButton>
            </Box>
          )}

          {copied && (
            <Typography variant="body2" color="success.main" sx={{ mb: 1 }}>
              ✅ Address copied!
            </Typography>
          )}
        </Box>

        <Divider />

        <MenuItem onClick={handleClose}>
          <Settings sx={{ mr: 2 }} />
          Settings
        </MenuItem>
        <MenuItem onClick={disconnectWallet}>
          <Logout sx={{ mr: 2, color: modernColors.error }} />
          <Typography color="error">Disconnect</Typography>
        </MenuItem>
      </Menu>

      {/* Notifications Menu */}
      <Menu
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            background: modernColors.gradients.card,
            border: `1px solid ${modernColors.dark.border.primary}`,
            borderRadius: 2,
            minWidth: 320,
            mt: 1,
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Notifications
          </Typography>

          {canClaim ? (
            <Card
              sx={{
                background: `${modernColors.success}20`,
                border: `1px solid ${modernColors.success}40`,
                mb: 2,
              }}
            >
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Typography variant="body1" sx={{ fontWeight: 600, color: modernColors.success }}>
                  🎉 Daily Reward Available!
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  You can claim {formatAmount(dailyReward)} tokens now
                </Typography>
              </CardContent>
            </Card>
          ) : timeUntilNextClaim > 0 ? (
            <Card
              sx={{
                background: `${modernColors.warning}20`,
                border: `1px solid ${modernColors.warning}40`,
                mb: 2,
              }}
            >
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Typography variant="body1" sx={{ fontWeight: 600, color: modernColors.warning }}>
                  ⏰ Next Claim Available In
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatTime(timeUntilNextClaim)}
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No new notifications
            </Typography>
          )}
        </Box>
      </Menu>
    </AppBar>
  );
};

export default ModernHeader;
